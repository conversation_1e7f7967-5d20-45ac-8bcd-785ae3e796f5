# Allyson Mobile Design System

## Table of Contents
1. [Design Tokens](#design-tokens)
2. [Color System](#color-system)
3. [Typography](#typography)
4. [Spacing](#spacing)
5. [Border Radius](#border-radius)
6. [Animations](#animations)
7. [Component Library](#component-library)
8. [Theme Configuration](#theme-configuration)

## Design Tokens

All design tokens are defined as CSS custom properties in `global.css` and mapped to Tailwind utilities.

### CSS Variables

```css
@layer base {
    :root {
        --background: 240 10% 3.9%;
        --foreground: 195 24% 99%;
        --border: 195 14% 14%;
        --muted: 15 38% 7%;
        --muted-foreground: 15 2% 74%;
        --popover: 195 32% 1%;
        --popover-foreground: 195 24% 99%;
        --card: 195 32% 1%;
        --card-foreground: 195 24% 99%;
        --input: 195 14% 14%;
        --primary: 195 100% 65%;
        --primary-foreground: 195 100% 5%;
        --secondary: 284.89 87.92% 70.78%;
        --secondary-foreground: 15 100% 5%;
        --accent: 284.89 87.92% 70.78%;
        --accent-foreground: 15 100% 5%;
        --destructive: 14.75 100% 64.9%;
        --destructive-foreground: 0 0% 0%;
        --ring: 195 100% 65%;
        --radius: 0.5rem;
    }
}
```

## Color System

### Primary Colors
- **Background**: `hsl(240 10% 3.9%)` - Very dark blue-gray (#0a0a0b)
- **Foreground**: `hsl(195 24% 99%)` - Almost white (#fcfdfd)
- **Primary**: `hsl(195 100% 65%)` - Bright cyan blue (#52d3ff)
- **Secondary**: `hsl(284.89 87.92% 70.78%)` - Bright purple (#d070f0)

### Semantic Colors
- **Destructive**: `hsl(14.75 100% 64.9%)` - Bright red/orange (#ff6847)
- **Muted**: `hsl(15 38% 7%)` - Very dark brown (#1d0f0a)
- **Border**: `hsl(195 14% 14%)` - Dark blue-gray (#1f2729)
- **Ring** (Focus): `hsl(195 100% 65%)` - Same as primary

### Navigation Theme Colors
```typescript
NAV_THEME = {
    light: {
      background: 'hsl(0 0% 100%)',
      border: 'hsl(240 5.9% 90%)',
      card: 'hsl(0 0% 100%)',
      notification: 'hsl(0 84.2% 60.2%)',
      primary: 'hsl(240 5.9% 10%)',
      text: 'hsl(240 10% 3.9%)',
    },
    dark: {
      background: 'hsl(240 10% 3.9%)',
      border: 'hsl(240 3.7% 15.9%)',
      card: 'hsl(240 10% 3.9%)',
      notification: 'hsl(0 72% 51%)',
      primary: 'hsl(0 0% 98%)',
      text: 'hsl(0 0% 98%)',
    },
}
```

## Typography

### Font Sizes
- `text-sm`: Small text
- `text-base`: Base text size
- `text-lg`: Large text
- `text-xl`: Extra large text
- `text-2xl`: 2x extra large
- `text-3xl`: 3x extra large
- `text-4xl`: 4x extra large
- `text-5xl`: 5x extra large

### Font Weights
- `font-medium`: Medium weight
- `font-semibold`: Semi-bold weight
- `font-extrabold`: Extra bold weight

### Typography Components

```tsx
// Typography component exports
export {
  H1,        // text-4xl font-extrabold text-zinc-400
  H2,        // text-3xl font-semibold with border-b
  H3,        // text-2xl font-semibold
  H4,        // text-xl font-semibold
  P,         // Regular paragraph (alias for Text)
  BlockQuote,// Italic with left border
  Code,      // bg-muted with padding
  Lead,      // text-xl text-muted-foreground
  Large,     // text-xl font-semibold
  Small,     // text-sm font-medium
  Muted,     // text-sm text-muted-foreground
  Text,      // Base text component
}
```

## Spacing

### Common Padding Values
- `p-2`: 0.5rem
- `p-4`: 1rem
- `p-6`: 1.5rem
- `px-3`: 0.75rem horizontal
- `px-4`: 1rem horizontal
- `py-2`: 0.5rem vertical
- `py-3`: 0.75rem vertical

### Common Margin Values
- `m-2`: 0.5rem
- `m-4`: 1rem
- `mt-4`: 1rem top
- `mt-6`: 1.5rem top
- `mb-2`: 0.5rem bottom

### Gap Utilities
- `gap-2`: 0.5rem
- `gap-4`: 1rem
- `space-y-1.5`: 0.375rem vertical spacing

## Border Radius

- Default radius: `0.5rem` (via `--radius` CSS variable)
- Common classes:
  - `rounded-md`: Medium radius
  - `rounded-lg`: Large radius
  - `rounded-full`: Full radius (circles)
  - `rounded-sm`: Small radius

## Animations

### Tailwind Animations
```javascript
keyframes: {
  "accordion-down": {
    from: { height: "0" },
    to: { height: "var(--radix-accordion-content-height)" },
  },
  "accordion-up": {
    from: { height: "var(--radix-accordion-content-height)" },
    to: { height: "0" },
  },
}
```

### Animation Classes
- `accordion-down`: 0.2s ease-out
- `accordion-up`: 0.2s ease-out
- Web animations: `animate-in`, `fade-in-0`, `zoom-in-95`

## Component Library

### UI Components (`/components/ui/`)
The project includes 40+ UI components:

#### Core Components
- **Accordion** - Collapsible content sections
- **Alert** - Alert messages
- **Alert Dialog** - Modal alerts
- **Aspect Ratio** - Maintain aspect ratios
- **Avatar** - User avatars
- **Badge** - Status indicators
- **Bottom Sheet** - Mobile bottom sheets
- **Button** - Interactive buttons
- **Calendar** - Date picker calendar
- **Card** - Content containers
- **Checkbox** - Checkbox inputs
- **Collapsible** - Collapsible content
- **Combobox** - Searchable select
- **Context Menu** - Right-click menus
- **Data Table** - Data tables
- **Dialog** - Modal dialogs
- **Dropdown Menu** - Dropdown menus
- **Form** - Form components
- **Hover Card** - Hover tooltips
- **Input** - Text inputs
- **Label** - Form labels
- **Menubar** - Menu bars
- **Navigation Menu** - Navigation menus
- **Popover** - Popover content
- **Progress** - Progress indicators
- **Radio Group** - Radio buttons
- **Select** - Select dropdowns
- **Separator** - Visual separators
- **Skeleton** - Loading skeletons
- **Slider** - Range sliders
- **Switch** - Toggle switches
- **Table** - Tables
- **Tabs** - Tab navigation
- **Text** - Text component
- **Textarea** - Multi-line text input
- **Toast** - Toast notifications
- **Toggle** - Toggle buttons
- **Toggle Group** - Grouped toggles
- **Tooltip** - Tooltips
- **Typography** - Text components

### Primitives (`/components/primitives/`)
Lower-level components based on Radix UI patterns, providing unstyled, accessible components.

### Custom Components
- **GradientButton** - Button with gradient overlay
- **ThemeToggle** - Theme switcher
- **Header** - App header
- **Loading** - Loading states
- **Icons** - Icon components

## Component Code

### Button Component

```tsx
import { cva } from "class-variance-authority";
import * as React from "react";
import { Pressable } from "react-native";
import { TextClassContext } from "~/components/ui/typography";
import { cn } from "~/lib/utils";

const buttonVariants = cva(
  "group flex items-center justify-center rounded-md web:ring-offset-background web:transition-colors web:focus-visible:outline-none web:focus-visible:ring-2 web:focus-visible:ring-ring web:focus-visible:ring-offset-2",
  {
    variants: {
      variant: {
        default: "bg-primary web:hover:opacity-90 active:opacity-90",
        destructive: "bg-destructive web:hover:opacity-90 active:opacity-90",
        outline:
          "border border-input bg-background web:hover:bg-accent web:hover:text-accent-foreground active:bg-zinc-400/20",
        secondary: "bg-secondary web:hover:opacity-80 active:opacity-80",
        ghost:
          "web:hover:bg-accent web:hover:text-accent-foreground active:bg-accent",
        link: "web:underline-offset-4 web:hover:underline web:focus:underline ",
      },
      size: {
        default: "h-10 px-4 py-2 native:h-12 native:px-5 native:py-3",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8 native:h-14",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

const buttonTextVariants = cva(
  "web:whitespace-nowrap text-sm native:text-base font-medium text-foreground web:transition-colors",
  {
    variants: {
      variant: {
        default: "text-primary-foreground",
        destructive: "text-destructive-foreground",
        outline: "group-active:text-foreground",
        secondary:
          "text-secondary-foreground group-active:text-secondary-foreground",
        ghost: "group-active:text-accent-foreground",
        link: "text-primary group-active:underline",
      },
      size: {
        default: "",
        sm: "",
        lg: "native:text-lg",
        icon: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

const Button = React.forwardRef(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <TextClassContext.Provider
        value={cn(
          props.disabled && "web:pointer-events-none",
          buttonTextVariants({ variant, size })
        )}
      >
        <Pressable
          className={cn(
            props.disabled && "opacity-50 web:pointer-events-none",
            buttonVariants({ variant, size, className })
          )}
          ref={ref}
          role="button"
          {...props}
        />
      </TextClassContext.Provider>
    );
  }
);
Button.displayName = "Button";

export { Button, buttonTextVariants, buttonVariants };
```

### Card Component

```tsx
import * as React from "react";
import { Text, View, Animated, Pressable } from "react-native";
import { TextClassContext } from "~/components/ui/typography";
import { cn } from "~/lib/utils";
import { LinearGradient } from "expo-linear-gradient";

const Card = React.forwardRef(({ className, ...props }, ref) => (
  <View
    ref={ref}
    className={cn(
      "rounded-lg border border-border bg-card shadow-sm shadow-foreground/10",
      className
    )}
    {...props}
  />
));
Card.displayName = "Card";

const GradientCard = React.forwardRef(
  ({ className, colors, start, end, ...props }, ref) => (
    <View
      ref={ref}
      className={cn(
        "rounded-lg border-2 border-border/60 shadow-sm shadow-foreground/10 overflow-hidden bg-background",
        className
      )}
      {...props}
    >
      <LinearGradient
        colors={
          colors || [
            "rgba(61, 169, 204, 0.9)",
            "rgba(69, 194, 236, 0.6)",
            "rgba(173, 93, 200, 0.5)",
            "rgba(173, 93, 200, 0.23)",
          ]
        }
        start={start || { x: 0, y: 0 }}
        end={end || { x: 1, y: 0 }}
        style={{
          position: "absolute",
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
          borderRadius: 8,
        }}
      />
      {props.children}
    </View>
  )
);
GradientCard.displayName = "GradientCard";

const AnimatedCard = React.forwardRef(({ className, onPress, ...props }, ref) => {
  const scaleAnim = React.useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Pressable
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={onPress}
    >
      <Animated.View
        ref={ref}
        style={{
          transform: [{ scale: scaleAnim }],
        }}
        className={cn(
          "rounded-lg border border-border bg-card shadow-sm shadow-foreground/10",
          className
        )}
        {...props}
      />
    </Pressable>
  );
});
AnimatedCard.displayName = "AnimatedCard";

const CardHeader = React.forwardRef(({ className, ...props }, ref) => (
  <View
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
));
CardHeader.displayName = "CardHeader";

const CardTitle = React.forwardRef(({ className, ...props }, ref) => (
  <Text
    role="heading"
    aria-level={3}
    ref={ref}
    className={cn(
      "text-2xl text-card-foreground font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
));
CardTitle.displayName = "CardTitle";

const CardDescription = React.forwardRef(({ className, ...props }, ref) => (
  <Text
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
));
CardDescription.displayName = "CardDescription";

const CardContent = React.forwardRef(({ className, ...props }, ref) => (
  <TextClassContext.Provider value="text-card-foreground">
    <View ref={ref} className={cn("p-6 pt-0", className)} {...props} />
  </TextClassContext.Provider>
));
CardContent.displayName = "CardContent";

const CardFooter = React.forwardRef(({ className, ...props }, ref) => (
  <View
    ref={ref}
    className={cn("flex flex-row items-center p-6 pt-0", className)}
    {...props}
  />
));
CardFooter.displayName = "CardFooter";

export {
  AnimatedCard,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  GradientCard,
};
```

### Badge Component

```tsx
import { cva } from "class-variance-authority";
import * as React from "react";
import { View } from "react-native";
import * as Slot from "~/components/primitives/slot";
import { cn } from "~/lib/utils";
import { TextClassContext } from "~/components/ui/typography";

const badgeVariants = cva(
  "web:inline-flex items-center rounded-md border px-2.5 py-0.5 web:transition-colors web:focus:outline-none web:focus:ring-2 web:focus:ring-ring web:focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary web:hover:opacity-80 active:opacity-80",
        secondary:
          "border-transparent bg-secondary web:hover:opacity-80 active:opacity-80",
        destructive:
          "border-transparent bg-destructive web:hover:opacity-80 active:opacity-80",
        outline: "text-foreground border-zinc-700/80",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

const badgeTextVariants = cva("text-xs font-semibold ", {
  variants: {
    variant: {
      default: "text-primary-foreground",
      secondary: "text-secondary-foreground",
      destructive: "text-destructive-foreground",
      outline: "text-foreground",
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

function Badge({ className, variant, asChild, ...props }) {
  const Component = asChild ? Slot.View : View;
  return (
    <TextClassContext.Provider value={badgeTextVariants({ variant })}>
      <Component
        className={cn(badgeVariants({ variant }), className)}
        {...props}
      />
    </TextClassContext.Provider>
  );
}

export { Badge, badgeTextVariants, badgeVariants };
```

### Input Component

```tsx
import * as React from "react";
import { 
  TextInput, 
  View, 
  Animated,
  Platform,
} from "react-native";
import { cn } from "~/lib/utils";
import { useState, useRef } from "react";
import * as Haptics from "expo-haptics";

// Regular Input component with keyboard awareness
const Input = React.forwardRef(
  ({ className, placeholderClassName, ...props }, ref) => {
    return (
      <TextInput
        ref={ref}
        className={cn(
          "web:flex h-10 native:h-12 web:w-full rounded-md border border-input bg-background px-2  text-sm native:text-lg text-foreground placeholder:text-muted-foreground web:ring-offset-background file:border-0 file:bg-transparent file:font-medium web:focus-visible:outline-none web:focus-visible:ring-2 web:focus-visible:ring-ring web:focus-visible:ring-offset-2",
          props.editable === false && "opacity-50 web:cursor-not-allowed",
          className
        )}
        placeholderClassName={cn("text-muted-foreground", placeholderClassName)}
        {...props}
      />
    );
  }
);

Input.displayName = "Input";

// Dynamic Text Input with enhanced keyboard handling
const DynamicTextInput = React.forwardRef(({ value, onChangeText }, ref) => {
  const [inputHeight, setInputHeight] = useState(32);
  const [isFocused, setIsFocused] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const lineHeight = 16;
  const maxLines = 6;
  const maxHeight = lineHeight * maxLines;

  const expandAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimations = [
    useRef(new Animated.Value(0)).current,
    useRef(new Animated.Value(0)).current,
    useRef(new Animated.Value(0)).current,
  ];

  const handleContentSizeChange = (event) => {
    const { height } = event.nativeEvent.contentSize;
    setInputHeight(Math.min(maxHeight, Math.max(32, height)));
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    Animated.parallel([
      Animated.timing(expandAnimation, {
        toValue: isExpanded ? 0 : 1,
        duration: 200,
        useNativeDriver: true,
      }),
      ...slideAnimations.map((anim, index) =>
        Animated.timing(anim, {
          toValue: isExpanded ? 0 : 1,
          duration: 200,
          delay: isExpanded ? 0 : index * 50,
          useNativeDriver: true,
        })
      ),
    ]).start();
  };

  const handleTextChange = (text) => {
    if (isExpanded) {
      setIsExpanded(false);
      toggleExpand();
    }
    onChangeText(text);
  };

  return (
    <View
      className="bg-background flex-row items-center"
      style={{
        minHeight: 64,
        maxHeight: maxHeight,
        borderRadius: 16,
        borderWidth: 1,
        borderColor: "#27272a",
        overflow: "hidden",
        marginBottom: 0,
      }}
    >
      <Animated.View style={{ flex: 1 }}>
        <TextInput
          ref={ref}
          value={value}
          onChangeText={handleTextChange}
          multiline
          onContentSizeChange={handleContentSizeChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onTouchStart={() => {
            if (isExpanded) {
              setIsExpanded(false);
              toggleExpand();
            }
          }}
          style={{
            paddingHorizontal: 16,
            paddingVertical: 6,
            fontSize: 16,
            lineHeight: lineHeight,
            minHeight: 32,
            maxHeight: maxHeight,
            color: '#e4e4e7',
          }}
          cursorColor="#a1a1aa"
          placeholder={isFocused ? "" : "Type a message..."}
          placeholderTextColor="#a1a1aa"
          textAlignVertical="top"
        />
      </Animated.View>
    </View>
  );
});

DynamicTextInput.displayName = "DynamicTextInput";

export { Input, DynamicTextInput };
```

### Dialog Component

```tsx
import { X } from "~/components/Icons";
import * as React from "react";
import { Platform, StyleSheet, View } from "react-native";
import Animated, { FadeIn, FadeOut } from "react-native-reanimated";
import * as DialogPrimitive from "~/components/primitives/dialog";
import { cn } from "~/lib/utils";

const Dialog = DialogPrimitive.Root;
const DialogTrigger = DialogPrimitive.Trigger;
const DialogPortal = DialogPrimitive.Portal;
const DialogClose = DialogPrimitive.Close;

const DialogOverlayWeb = React.forwardRef(({ className, ...props }, ref) => {
  const { open } = DialogPrimitive.useContext();
  return (
    <DialogPrimitive.Overlay
      style={StyleSheet.absoluteFill}
      className={cn(
        "z-50 bg-black/80 flex justify-center items-center p-2",
        open
          ? "web:animate-in web:fade-in-0"
          : "web:animate-out web:fade-out-0",
        className
      )}
      {...props}
      ref={ref}
    />
  );
});
DialogOverlayWeb.displayName = "DialogOverlayWeb";

const DialogOverlayNative = React.forwardRef(
  ({ className, children, ...props }, ref) => {
    return (
      <DialogPrimitive.Overlay
        style={StyleSheet.absoluteFill}
        className={cn(
          "z-50 flex bg-black/80 justify-center items-center p-2",
          className
        )}
        {...props}
        ref={ref}
      >
        <Animated.View
          entering={FadeIn.duration(150)}
          exiting={FadeOut.duration(150)}
        >
          <>{children}</>
        </Animated.View>
      </DialogPrimitive.Overlay>
    );
  }
);
DialogOverlayNative.displayName = "DialogOverlayNative";

const DialogOverlay = Platform.select({
  web: DialogOverlayWeb,
  default: DialogOverlayNative,
});

const DialogContent = React.forwardRef(
  ({ className, children, ...props }, ref) => {
    const { open } = DialogPrimitive.useContext();
    return (
      <DialogPortal>
        <DialogOverlay>
          <DialogPrimitive.Content
            ref={ref}
            className={cn(
              "z-50 max-w-lg gap-4 web:cursor-default bg-background border border-zinc-800 p-6 shadow-lg web:duration-200 rounded-lg",
              open
                ? "web:animate-in web:fade-in-0 web:zoom-in-95"
                : "web:animate-out web:fade-out-0 web:zoom-out-95",
              className
            )}
            {...props}
          >
            {children}
          </DialogPrimitive.Content>
        </DialogOverlay>
      </DialogPortal>
    );
  }
);
DialogContent.displayName = DialogPrimitive.Content.displayName;

const DialogHeader = ({ className, ...props }) => (
  <View
    className={cn("flex flex-col gap-1.5 text-center sm:text-left", className)}
    {...props}
  />
);
DialogHeader.displayName = "DialogHeader";

const DialogFooter = ({ className, ...props }) => (
  <View
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end gap-2",
      className
    )}
    {...props}
  />
);
DialogFooter.displayName = "DialogFooter";

const DialogTitle = React.forwardRef(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      "text-lg native:text-xl text-foreground font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
));
DialogTitle.displayName = DialogPrimitive.Title.displayName;

const DialogDescription = React.forwardRef(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn("text-sm native:text-base text-muted-foreground", className)}
    {...props}
  />
));
DialogDescription.displayName = DialogPrimitive.Description.displayName;

export {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  DialogTrigger,
};
```

### Switch Component

```tsx
import * as React from "react";
import { Platform } from "react-native";
import Animated, {
  interpolateColor,
  useAnimatedStyle,
  useDerivedValue,
  withTiming,
} from "react-native-reanimated";
import * as SwitchPrimitives from "~/components/primitives/switch";
import { useColorScheme } from "~/lib/useColorScheme";
import { cn } from "~/lib/utils";

const SwitchWeb = React.forwardRef(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      "peer flex-row h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed",
      props.checked ? "bg-primary" : "bg-input",
      props.disabled && "opacity-50",
      className
    )}
    {...props}
    ref={ref}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        "pointer-events-none block h-5 w-5 rounded-full bg-background shadow-md shadow-foreground/5 ring-0 transition-transform",
        props.checked ? "translate-x-5" : "translate-x-0"
      )}
    />
  </SwitchPrimitives.Root>
));
SwitchWeb.displayName = "SwitchWeb";

const RGB_COLORS = {
  light: {
    primary: "#a1a1aa",
    input: "#a1a1aa",
  },
  dark: {
    primary: "#a1a1aa",
    input: "#a1a1aa",
  },
};

const SwitchNative = React.forwardRef(({ className, ...props }, ref) => {
  const { colorScheme } = useColorScheme();
  const translateX = useDerivedValue(() => (props.checked ? 18 : 0));
  const animatedRootStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: interpolateColor(
        translateX.value,
        [0, 18],
        [RGB_COLORS[colorScheme].primary, RGB_COLORS[colorScheme].input]
      ),
    };
  });
  const animatedThumbStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: withTiming(translateX.value, { duration: 200 }) },
    ],
  }));
  return (
    <Animated.View
      style={animatedRootStyle}
      className={cn(
        "h-8 w-[46px] rounded-full",
        props.disabled && "opacity-50"
      )}
    >
      <SwitchPrimitives.Root
        className={cn(
          "flex-row h-8 w-[46px] shrink-0 items-center rounded-full border-2 border-transparent",
          className
        )}
        {...props}
        ref={ref}
      >
        <Animated.View style={animatedThumbStyle}>
          <SwitchPrimitives.Thumb
            className={
              "h-7 w-7 rounded-full bg-background shadow-md shadow-foreground/25 ring-0"
            }
          />
        </Animated.View>
      </SwitchPrimitives.Root>
    </Animated.View>
  );
});
SwitchNative.displayName = "SwitchNative";

const Switch = Platform.select({
  web: SwitchWeb,
  default: SwitchNative,
});

export { Switch };
```

### GradientButton Component

```tsx
import React from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Text } from "./ui/typography";

const GradientButton = ({ text, onPress, icon }) => {
  return (
    <TouchableOpacity className="w-full" onPress={onPress}>
      <LinearGradient
        colors={["rgba(255, 255, 255, 0.9)", "rgba(255, 255, 255, 0.5)"]}
        style={styles.button}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
      >
        <View style={styles.contentContainer}>
          {icon && <View style={styles.iconContainer}>{icon}</View>}
          <View style={styles.textContainer}>
            <Text className="text-zinc-900 font-semibold text-lg">{text}</Text>
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    flexDirection: "row",
    alignItems: "center",
    width: '100%',
  },
  iconContainer: {
    position: 'absolute',
    left: 0,
    zIndex: 1,
  },
  textContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  button: {
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 15,
  },
  buttonText: {
    color: "#71717a",
    fontWeight: "bold",
    fontSize: 16,
  },
});

export default GradientButton;
```

## Theme Configuration

### Tailwind Configuration (`tailwind.config.js`)

```javascript
const { hairlineWidth } = require("nativewind/theme");

module.exports = {
  darkMode: "class",
  content: ["./app/**/*.{js,jsx,ts,tsx}", "./components/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderWidth: {
        hairline: hairlineWidth(),
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("nativewind/preset")],
};
```

### Theme Implementation
- **Dark Mode First**: The app is built with a dark theme as the default
- **Theme Persistence**: Themes are saved using AsyncStorage
- **Android Navigation Bar**: Syncs with current theme
- **CSS Variables**: All colors use CSS custom properties for easy theming
- **Platform-Specific**: Handles both web and native platforms

## Usage Guidelines

### Component Variants
Components use class-variance-authority (CVA) for consistent variant handling:
- **Button**: default, destructive, outline, secondary, ghost, link
- **Badge**: default, secondary, destructive, outline
- **Card**: Regular, GradientCard, AnimatedCard

### Accessibility
- All interactive components include proper ARIA roles
- Focus management with ring utilities
- Keyboard navigation support on web
- Screen reader support

### Responsive Design
- Mobile-first approach
- Native-specific styles with `native:` prefix
- Web-specific styles with `web:` prefix
- Platform-specific component implementations

### Performance
- Optimized animations with `useNativeDriver`
- Lazy component loading
- Reanimated for smooth animations
- Haptic feedback on native platforms