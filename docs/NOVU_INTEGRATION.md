# Novu Integration Guide

This document explains how Novu notifications are integrated into the Allyson mobile app.

## Overview

Novu is an open-source notification infrastructure that provides:
- **In-app notifications** with a customizable inbox component
- **Push notifications** through multiple providers (FCM, APNS, Expo Push, etc.)
- **Multi-channel delivery** (email, SMS, chat)
- **Workflow automation** and user preferences
- **Unified API** for all notification types

## Architecture

### Components

1. **NovuService** (`lib/novuService.ts`)
   - Handles permission requests
   - Manages push tokens
   - Communicates with backend for notification settings

2. **NovuInbox** (`components/novu/NovuInbox.tsx`)
   - Custom inbox UI using Novu React Native hooks
   - Displays notifications with read/unread states
   - Handles notification interactions

3. **Notification Dialogs**
   - `NovuNotificationsDialog` - Settings dialog
   - `NovuEnableNotificationsDialog` - Enable notifications prompt
   - `NovuNotificationsScreen` - Onboarding screen

4. **Hooks**
   - `useNovuNotifications` - Manages notification setup and handling

### Provider Setup

The app uses `NovuProviderWrapper` to provide user-specific subscriber IDs to the Novu context.

```tsx
<NovuProviderWrapper>
  <YourAppComponents />
</NovuProviderWrapper>
```

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
EXPO_PUBLIC_NOVU_APP_ID=your_novu_application_identifier
EXPO_PUBLIC_NOVU_BACKEND_URL=https://api.novu.co
```

### Novu Dashboard Setup

1. Create a Novu account at [dashboard.novu.co](https://dashboard.novu.co)
2. Get your Application Identifier from the API Keys page
3. Set up notification workflows in the dashboard
4. Configure push providers (FCM, APNS, etc.)

## Usage

### Basic Inbox

```tsx
import { NovuInbox } from '~/components/novu/NovuInbox';

function NotificationsScreen() {
  return (
    <NovuInbox 
      onNotificationPress={(notification) => {
        // Handle notification tap
        console.log('Notification pressed:', notification);
      }}
    />
  );
}
```

### Notification Setup

```tsx
import { useNovuNotifications } from '~/hooks/useNovuNotifications';

function MyComponent() {
  const { setupNotifications, handleNotificationPress } = useNovuNotifications();

  const enableNotifications = async () => {
    const success = await setupNotifications();
    if (success) {
      console.log('Notifications enabled!');
    }
  };

  return (
    <NovuInbox onNotificationPress={handleNotificationPress} />
  );
}
```

## Backend Integration

### API Endpoints

Update your backend to work with Novu:

```typescript
// Update notification settings endpoint
POST /user/update-notifications
{
  "mobile": boolean,
  "email": boolean,
  "subscriberId": string,
  "novuPushToken": string,
  "pushTokenType": "fcm" | "apns" | "expo"
}
```

### Triggering Notifications

Use Novu's server SDK to trigger notifications:

```typescript
import { Novu } from '@novu/node';

const novu = new Novu('<NOVU_SECRET_KEY>');

// Trigger a notification
await novu.trigger('workflow-identifier', {
  to: {
    subscriberId: 'user-id',
  },
  payload: {
    sessionId: 'session-123',
    message: 'Allyson needs your help!',
  },
});
```

## Migration from Expo Notifications

### What Changed

1. **Removed**: `expo-notifications` dependency
2. **Added**: `@novu/react-native` for in-app notifications
3. **Enhanced**: Multi-channel notification support
4. **Improved**: User preference management

### Key Benefits

- **Unified API**: Single interface for all notification types
- **Better UX**: Built-in inbox component with read/unread states
- **Scalability**: Workflow automation and multi-channel delivery
- **Analytics**: Built-in notification analytics and tracking

### Breaking Changes

- Push token format may change (handled automatically)
- Notification payload structure updated
- New environment variables required

## Testing Guide

### Manual Testing Checklist

#### 1. Permission Handling
- [ ] Test permission request on first app launch
- [ ] Test permission status checking
- [ ] Test handling of denied permissions
- [ ] Test permission re-request flow

#### 2. Device Registration
- [ ] Test device registration with Novu
- [ ] Verify push token generation
- [ ] Test subscriber ID assignment
- [ ] Test registration error handling

#### 3. Notification Settings
- [ ] Test enabling notifications in onboarding
- [ ] Test notification settings dialog
- [ ] Test updating notification preferences
- [ ] Test backend API integration

#### 4. Push Notifications
- [ ] Test receiving push notifications when app is closed
- [ ] Test receiving push notifications when app is backgrounded
- [ ] Test foreground notification handling
- [ ] Test notification tap navigation

#### 5. In-App Notifications
- [ ] Test NovuInbox component rendering
- [ ] Test notification read/unread states
- [ ] Test notification interactions
- [ ] Test notification removal

#### 6. Cross-Platform Testing
- [ ] Test on iOS physical device
- [ ] Test on Android physical device
- [ ] Test on iOS simulator (limited functionality)
- [ ] Test on Android emulator (limited functionality)

### Testing Commands

```bash
# Install dependencies
bun install

# Start development server
bun start:local

# Build for testing
eas build --platform ios --profile development
eas build --platform android --profile development

# Run on device
bun ios
bun android
```

### Test Notification Payload

```json
{
  "to": "ExponentPushToken[...]",
  "title": "Test Notification",
  "body": "This is a test notification from Novu",
  "data": {
    "sessionId": "test-session-123",
    "url": "/(drawer)/(sessions)/session?id=test-session-123",
    "tool": "test-tool",
    "toolsOutput": "{\"result\": \"test\"}"
  }
}
```

## Troubleshooting

### Common Issues

1. **Notifications not appearing**
   - Check NOVU_APP_ID is correct
   - Verify subscriber ID is set properly
   - Ensure workflows are active in Novu dashboard

2. **Push notifications not working**
   - Verify push provider setup in Novu dashboard
   - Check device token registration
   - Ensure proper permissions are granted

3. **Inbox not loading**
   - Check network connectivity
   - Verify Novu provider is properly wrapped
   - Check console for API errors

### Debug Mode

Enable debug logging:

```typescript
// In your app initialization
console.log('Novu Config:', {
  appId: process.env.EXPO_PUBLIC_NOVU_APP_ID,
  subscriberId: userId,
});
```

## Next Steps

1. Set up Novu dashboard and workflows
2. Configure push notification providers
3. Test notification delivery
4. Customize inbox UI as needed
5. Set up analytics and monitoring

For more information, visit [Novu Documentation](https://docs.novu.co).
