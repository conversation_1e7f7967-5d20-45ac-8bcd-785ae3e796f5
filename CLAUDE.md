# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Allyson is a React Native mobile application (iOS and Android) built with Expo SDK 53. It's an AI web agent that handles online tasks with features including authentication, push notifications, in-app purchases, and MCP (Model Context Protocol) integration for human-in-the-loop interfaces.

## Common Development Commands

### Development
```bash
# Start development server with local environment
npm run start:local

# Start preview server (test environment)
npm run start:preview

# Start production server
npm run start:production
```

### Building
```bash
# Development builds
npm run eas:build:development:ios
npm run eas:build:development:android

# Preview builds
npm run eas:build:preview:ios
npm run eas:build:preview:android

# Production builds
npm run eas:build:production:ios
npm run eas:build:production:android
```

### Linting and Type Checking
The project uses TypeScript with strict mode. Run type checking with:
```bash
npx tsc --noEmit
```

## Architecture

### Navigation Structure
Uses Expo Router (file-based routing):
- `app/(guest)/` - Public routes (sign-in, landing)
- `app/(auth)/` - Authenticated routes
  - `(drawer)/` - Main drawer navigation
    - `(sessions)/` - Session management screens
    - `(settings)/` - Settings screens including MCP configuration
  - `(onboarding)/` - User onboarding flow

### Key Technologies
- **Styling**: NativeWind (Tailwind CSS for React Native) with `global.css` as the entry point
- **State Management**: Zustand for global state
- **Forms**: React Hook Form with Zod validation
- **Authentication**: Clerk
- **Analytics**: PostHog
- **Payments**: React Native Adapty for subscriptions
- **UI Components**: Custom primitives based on Radix UI patterns in `components/primitives/`

### MCP Integration
The app includes Model Context Protocol support for human-in-the-loop interfaces:
- WebSocket connection to MCP server (default: `ws://localhost:3001`)
- Dialog system for user prompts with various types (input, choice, multiline, confirmation, info)
- Auto-reconnection with exponential backoff
- Context provider in `context/MCPContext.tsx`

### Environment Configuration
Required environment variables:
- `EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY` - Clerk authentication
- `EXPO_PUBLIC_HOST_URL` - App host URL
- `EXPO_PUBLIC_API_URL` - Backend API URL
- `POSTHOG_API_KEY` - Analytics

Create `.env.development` and `.env.production` from the example files.

### Important Configuration Files
- `babel.config.js` - Configured for NativeWind and Reanimated
- `metro.config.js` - Metro bundler with NativeWind integration
- `app.config.js` - Dynamic Expo configuration with plugins
- `eas.json` - EAS Build configuration (create from `example.eas.json`)

## License
GNU Affero General Public License v3.0 (AGPL-3.0) with dual licensing option for commercial use.