import { Platform } from 'react-native';
import * as Device from 'expo-device';
import * as Notifications from 'expo-notifications';
import Toast from 'react-native-toast-message';

// Novu configuration
export const NOVU_CONFIG = {
  applicationIdentifier: process.env.EXPO_PUBLIC_NOVU_APP_ID || '',
  backendUrl: process.env.EXPO_PUBLIC_NOVU_BACKEND_URL || 'https://api.novu.co',
};

export interface NotificationPermissionStatus {
  status: 'granted' | 'denied' | 'undetermined';
}

export interface PushTokenData {
  token: string;
  type: 'expo' | 'fcm' | 'apns';
}

/**
 * Novu-based notification service
 * Handles permission requests, token management, and notification setup
 */
export class NovuNotificationService {
  private static instance: NovuNotificationService;
  private isInitialized = false;

  public static getInstance(): NovuNotificationService {
    if (!NovuNotificationService.instance) {
      NovuNotificationService.instance = new NovuNotificationService();
    }
    return NovuNotificationService.instance;
  }

  /**
   * Initialize notification handlers and settings
   */
  initialize(): void {
    if (this.isInitialized) {
      return;
    }

    // Set notification handler for foreground notifications
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: false,
        shouldSetBadge: false,
      }),
    });

    this.isInitialized = true;
    console.log('Novu notification service initialized');
  }

  /**
   * Check current notification permission status
   */
  async checkPermissionStatus(): Promise<NotificationPermissionStatus> {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      return { status: status as 'granted' | 'denied' | 'undetermined' };
    } catch (error) {
      console.error('Error checking notification permissions:', error);
      return { status: 'denied' };
    }
  }

  /**
   * Request notification permissions from the user
   */
  async requestPermissions(): Promise<NotificationPermissionStatus> {
    try {
      if (!Device.isDevice) {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Must use physical device for Push Notifications',
        });
        return { status: 'denied' };
      }

      const { status } = await Notifications.requestPermissionsAsync();
      return { status: status as 'granted' | 'denied' | 'undetermined' };
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to request notification permissions',
      });
      return { status: 'denied' };
    }
  }

  /**
   * Get push token for the device
   * This will be used to register the device with Novu
   */
  async getPushToken(): Promise<PushTokenData | null> {
    try {
      if (!Device.isDevice) {
        console.warn('Push tokens are only available on physical devices');
        return null;
      }

      // Get Expo push token
      const { data: token } = await Notifications.getExpoPushTokenAsync({
        projectId: "437dc3c2-90df-46f0-8330-16d9a3a72355",
      });

      return {
        token,
        type: 'expo',
      };
    } catch (error) {
      console.error('Error getting push token:', error);
      return null;
    }
  }

  /**
   * Register device with Novu for push notifications
   */
  async registerDevice(subscriberId: string, pushToken: PushTokenData): Promise<boolean> {
    try {
      // This would typically make an API call to your backend
      // which then registers the device with Novu
      console.log('Registering device with Novu:', {
        subscriberId,
        pushToken,
      });

      // Simulate successful registration
      return true;
    } catch (error) {
      console.error('Error registering device with Novu:', error);
      return false;
    }
  }

  /**
   * Update notification settings on the backend
   */
  async updateNotificationSettings(
    token: string,
    settings: {
      mobile: boolean;
      email: boolean;
      subscriberId: string;
      pushToken?: PushTokenData;
    }
  ): Promise<boolean> {
    try {
      const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/user/update-notifications`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          mobile: settings.mobile,
          email: settings.email,
          subscriberId: settings.subscriberId,
          novuPushToken: settings.pushToken?.token,
          pushTokenType: settings.pushToken?.type,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Notification settings updated:', data);

      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: 'Notification Settings Updated',
      });

      return true;
    } catch (error) {
      console.error('Error updating notification settings:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to update notification settings',
      });
      return false;
    }
  }

  /**
   * Setup notification channels for Android
   */
  async setupNotificationChannels(): Promise<void> {
    try {
      if (Platform.OS === 'android') {
        // Setup default notification channel
        console.log('Setting up Android notification channels');
        // This would typically configure notification channels
        // For now, we'll just log the setup
      }
    } catch (error) {
      console.error('Error setting up notification channels:', error);
    }
  }

  /**
   * Initialize the notification service
   */
  async initialize(): Promise<void> {
    try {
      await this.setupNotificationChannels();
      console.log('Novu notification service initialized');
    } catch (error) {
      console.error('Error initializing notification service:', error);
    }
  }
}

// Export singleton instance
export const novuNotificationService = NovuNotificationService.getInstance();
