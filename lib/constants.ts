export const NAV_THEME = {
    light: {
      background: 'hsl(0 0% 100%)',
      border: 'hsl(240 5.9% 90%)',
      card: 'hsl(0 0% 100%)',
      notification: 'hsl(0 84.2% 60.2%)',
      primary: 'hsl(240 5.9% 10%)',
      text: 'hsl(240 10% 3.9%)',
    },
    dark: {
      background: 'hsl(240 10% 3.9%)',
      border: 'hsl(240 3.7% 15.9%)',
      card: 'hsl(240 10% 3.9%)',
      notification: 'hsl(0 72% 51%)',
      primary: 'hsl(0 0% 98%)',
      text: 'hsl(0 0% 98%)',
    },
  };
  
  export const COMPONENTS = [
    'accordion',
    'alert',
    'alert-dialog',
    'aspect-ratio',
    'avatar',
    'badge',
    'bottom-sheet',
    'button',
    'calendar',
    'card',
    'checkbox',
    'combobox',
    'collapsible',
    'command',
    'context-menu',
    'data-table',
    'date-picker',
    'dialog',
    'dropdown-menu',
    'form',
    'hover-card',
    'input',
    'label',
    'material-top-tabs',
    'menubar',
    'navigation-menu',
    'popover',
    'progress',
    'radio-group',
    'select',
    'separator',
    'skeleton',
    'slider',
    'switch',
    'table',
    'tabs',
    'textarea',
    'toast',
    'toggle',
    'toggle-group',
    'toolbar',
    'tooltip',
    'typography',
  ];
  
  export const PRIMITIVES = [
    'accordion',
    'alert-dialog',
    'aspect-ratio',
    'avatar',
    'checkbox',
    'collapsible',
    'context-menu',
    'dialog',
    'dropdown-menu',
    'hover-card',
    'label',
    'menubar',
    'navigation-menu',
    'popover',
    'portal',
    'progress',
    'radio-group',
    'select',
    'separator',
    'slider',
    'switch',
    'table',
    'tabs',
    'toast',
    'toggle',
    'toggle-group',
    'toolbar',
    'tooltip',
  ];
  