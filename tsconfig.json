{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "paths": {"~/*": ["*"]}, "jsx": "react-native", "target": "esnext", "lib": ["esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true}, "include": ["**/*.ts", "**/*.tsx", "nativewind-env.d.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js"]}