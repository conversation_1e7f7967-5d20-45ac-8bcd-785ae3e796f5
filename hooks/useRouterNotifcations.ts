import { useRouter } from "expo-router";
import React from "react";
import { useNovuNotifications } from "./useNovuNotifications";

export function useRouterNotifications() {
  const router = useRouter();
  const { handleNotificationPress } = useNovuNotifications();

  React.useEffect(() => {
    console.log("Router notifications hook initialized with Novu");
    // The actual notification handling is now managed by useNovuNotifications hook
    // which handles notification press events and routing
  }, []);

  // Return the notification handler for external use if needed
  return {
    handleNotificationPress,
  };
}
