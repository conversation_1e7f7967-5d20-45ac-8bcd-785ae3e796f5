import { useEffect, useState } from 'react';
import { useAuth } from '@clerk/clerk-expo';
import { useUser } from '~/context/UserContext';
import { novuNotificationService } from '~/lib/novuService';
import { router } from 'expo-router';
import * as Notifications from 'expo-notifications';

export interface NovuNotificationHook {
  isInitialized: boolean;
  subscriberId: string | null;
  setupNotifications: () => Promise<boolean>;
  handleNotificationPress: (notification: any) => void;
}

/**
 * Hook to manage Novu notifications with user context
 */
export const useNovuNotifications = (): NovuNotificationHook => {
  const { user } = useUser();
  const { userId } = useAuth();
  const [isInitialized, setIsInitialized] = useState(false);
  const [subscriberId, setSubscriberId] = useState<string | null>(null);

  useEffect(() => {
    // Set subscriber ID when user is available
    if (userId) {
      setSubscriberId(userId);
      setIsInitialized(true);
    } else if (user?.id) {
      setSubscriberId(user.id);
      setIsInitialized(true);
    }
  }, [userId, user]);

  /**
   * Handle notification press/tap
   */
  const handleNotificationPress = (notification: any) => {
    try {
      console.log('Notification pressed:', notification);

      // Extract data from notification
      const data = notification.request?.content?.data || notification.data || notification.payload || {};

      // Handle different types of notifications
      if (data.sessionId) {
        // Navigate to session page
        router.push(`/(drawer)/(sessions)/session?id=${data.sessionId}`);
      } else if (data.url) {
        // Navigate to specific URL
        router.push(data.url);
      } else if (data.tool && data.toolsOutput) {
        // Handle tool-related notifications
        router.push({
          pathname: data.url || '/(drawer)/home',
          params: {
            tool: data.tool,
            toolsOutput: JSON.stringify(data.toolsOutput),
          },
        });
      } else {
        // Default action - navigate to home
        router.push('/(drawer)/home');
      }
    } catch (error) {
      console.error('Error handling notification press:', error);
      // Fallback to home
      router.push('/(drawer)/home');
    }
  };

  useEffect(() => {
    // Initialize notification service
    novuNotificationService.initialize();

    // Set up notification event listeners
    const notificationListener = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received in foreground:', notification);
      // Handle foreground notifications if needed
    });

    const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response received:', response);
      handleNotificationPress(response.notification);
    });

    // Handle notifications that were received while app was closed
    Notifications.getLastNotificationResponseAsync().then(response => {
      if (response) {
        console.log('Last notification response:', response);
        handleNotificationPress(response.notification);
      }
    });

    return () => {
      notificationListener.remove();
      responseListener.remove();
    };
  }, [handleNotificationPress]);

  /**
   * Setup notifications for the current user
   */
  const setupNotifications = async (): Promise<boolean> => {
    try {
      if (!subscriberId) {
        console.warn('No subscriber ID available for notification setup');
        return false;
      }

      // Check permissions
      const permissionStatus = await novuNotificationService.checkPermissionStatus();
      
      if (permissionStatus.status !== 'granted') {
        const requestResult = await novuNotificationService.requestPermissions();
        if (requestResult.status !== 'granted') {
          return false;
        }
      }

      // Get push token
      const pushToken = await novuNotificationService.getPushToken();
      if (!pushToken) {
        console.warn('Failed to get push token');
        return false;
      }

      // Register device with Novu
      const registered = await novuNotificationService.registerDevice(subscriberId, pushToken);
      
      return registered;
    } catch (error) {
      console.error('Error setting up notifications:', error);
      return false;
    }
  };



  return {
    isInitialized,
    subscriberId,
    setupNotifications,
    handleNotificationPress,
  };
};
