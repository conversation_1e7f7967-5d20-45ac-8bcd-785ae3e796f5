$ NODE_ENV=development expo start -c
Starting project at /Users/<USER>/Projects/Human-In-the-Loop-MCP-Server/allyson-mobile
env: load .env.development
env: export APP_ENV
Starting Metro Bundler
warning: Bundler cache is empty, rebuilding (this may take a minute)
Waiting on http://localhost:8081
Logs for your project will appear below.
Web Bundling failed 108938ms (node_modules/expo-router/entry.js)
Unable to resolve "../package.json" from "node_modules/@superwall/react-native-superwall/lib/module/index.js"
