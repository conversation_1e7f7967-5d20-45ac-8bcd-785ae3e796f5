{"permissions": {"allow": ["Bash(npm install:*)", "Bash(npx expo prebuild:*)", "Bash(npx expo run:ios:*)", "Bash(npx expo start:*)", "mcp__xcode-mcp-server__set_project_path", "mcp__xcode-mcp-server__list_available_schemes", "mcp__xcode-mcp-server__build_project", "mcp__xcode-mcp-server__get_project_configuration", "mcp__xcode-mcp-server__run_xcrun", "Bash(RCT_METRO_PORT=8083 npx react-native run-ios --simulator=\"iPhone 16\")", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(curl:*)"], "deny": []}}