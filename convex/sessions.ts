import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { auth } from "./auth";

export const createSession = mutation({
  args: {
    title: v.string(),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const identity = await auth.getUserIdentity(ctx);
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    const sessionId = await ctx.db.insert("sessions", {
      userId: user._id,
      title: args.title,
      status: "active",
      startedAt: Date.now(),
      metadata: args.metadata,
    });

    return await ctx.db.get(sessionId);
  },
});

export const listSessions = query({
  args: {
    status: v.optional(v.union(v.literal("active"), v.literal("completed"), v.literal("cancelled"))),
  },
  handler: async (ctx, args) => {
    const identity = await auth.getUserIdentity(ctx);
    if (!identity) {
      return [];
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user) {
      return [];
    }

    let query = ctx.db
      .query("sessions")
      .withIndex("by_user", (q) => q.eq("userId", user._id));

    const sessions = await query.collect();

    if (args.status) {
      return sessions.filter(session => session.status === args.status);
    }

    return sessions.sort((a, b) => b.startedAt - a.startedAt);
  },
});

export const getSession = query({
  args: {
    sessionId: v.id("sessions"),
  },
  handler: async (ctx, args) => {
    const identity = await auth.getUserIdentity(ctx);
    if (!identity) {
      return null;
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      return null;
    }

    // Verify the session belongs to the current user
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user || session.userId !== user._id) {
      return null;
    }

    return session;
  },
});

export const updateSessionStatus = mutation({
  args: {
    sessionId: v.id("sessions"),
    status: v.union(v.literal("active"), v.literal("completed"), v.literal("cancelled")),
  },
  handler: async (ctx, args) => {
    const identity = await auth.getUserIdentity(ctx);
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const session = await ctx.db.get(args.sessionId);
    if (!session) {
      throw new Error("Session not found");
    }

    // Verify the session belongs to the current user
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk", (q) => q.eq("clerkId", identity.subject))
      .first();

    if (!user || session.userId !== user._id) {
      throw new Error("Unauthorized");
    }

    const updates: any = {
      status: args.status,
    };

    if (args.status === "completed" || args.status === "cancelled") {
      updates.completedAt = Date.now();
    }

    await ctx.db.patch(args.sessionId, updates);

    return await ctx.db.get(args.sessionId);
  },
});