import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  users: defineTable({
    name: v.optional(v.string()),
    email: v.string(),
    clerkId: v.string(),
    imageUrl: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
  }).index("by_clerk", ["clerkId"]).index("by_email", ["email"]),
  
  sessions: defineTable({
    userId: v.id("users"),
    title: v.string(),
    status: v.union(v.literal("active"), v.literal("completed"), v.literal("cancelled")),
    startedAt: v.number(),
    completedAt: v.optional(v.number()),
    metadata: v.optional(v.any()),
  }).index("by_user", ["userId"]).index("by_status", ["status"]),
});