import React from 'react';
import {
  FlatList,
  View,
  Text,
  ActivityIndicator,
  RefreshControl,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { useNotifications } from '@novu/react-native';
import { Card } from '~/components/ui/card';
import { IconBell, IconCheck, IconX } from '@tabler/icons-react-native';
import * as Haptics from 'expo-haptics';

interface NovuInboxProps {
  className?: string;
  onNotificationPress?: (notification: any) => void;
}

export const NovuInbox: React.FC<NovuInboxProps> = ({ 
  className = '',
  onNotificationPress 
}) => {
  const { 
    notifications, 
    isLoading, 
    fetchMore, 
    hasMore, 
    refetch,
    markAsRead,
    markAsUnread,
    remove
  } = useNotifications();

  const handleNotificationPress = (notification: any) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    // Mark as read if unread
    if (!notification.read) {
      markAsRead(notification.id);
    }
    
    // Call custom handler if provided
    if (onNotificationPress) {
      onNotificationPress(notification);
    }
  };

  const handleMarkAsRead = (notificationId: string, event: any) => {
    event.stopPropagation();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    markAsRead(notificationId);
  };

  const handleRemove = (notificationId: string, event: any) => {
    event.stopPropagation();
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    remove(notificationId);
  };

  const renderNotificationItem = ({ item: notification }) => (
    <TouchableOpacity
      onPress={() => handleNotificationPress(notification)}
      className="mb-3"
    >
      <Card className={`p-4 ${!notification.read ? 'border-blue-500 bg-blue-50/10' : 'bg-zinc-900'}`}>
        <View className="flex-row items-start justify-between">
          <View className="flex-1 mr-3">
            {/* Notification Icon */}
            <View className="flex-row items-center mb-2">
              <View className={`w-8 h-8 rounded-full items-center justify-center mr-3 ${
                !notification.read ? 'bg-blue-500' : 'bg-zinc-600'
              }`}>
                <IconBell size={16} color="#fff" />
              </View>
              <Text className={`font-semibold ${
                !notification.read ? 'text-zinc-100' : 'text-zinc-300'
              }`}>
                {notification.subject || 'Notification'}
              </Text>
            </View>

            {/* Notification Body */}
            <Text className={`text-sm leading-5 ${
              !notification.read ? 'text-zinc-200' : 'text-zinc-400'
            }`}>
              {notification.body || 'No content available'}
            </Text>

            {/* Timestamp */}
            {notification.createdAt && (
              <Text className="text-xs text-zinc-500 mt-2">
                {new Date(notification.createdAt).toLocaleDateString()} at{' '}
                {new Date(notification.createdAt).toLocaleTimeString([], { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </Text>
            )}
          </View>

          {/* Action Buttons */}
          <View className="flex-row items-center space-x-2">
            {!notification.read && (
              <TouchableOpacity
                onPress={(e) => handleMarkAsRead(notification.id, e)}
                className="w-8 h-8 rounded-full bg-green-600 items-center justify-center"
              >
                <IconCheck size={14} color="#fff" />
              </TouchableOpacity>
            )}
            
            <TouchableOpacity
              onPress={(e) => handleRemove(notification.id, e)}
              className="w-8 h-8 rounded-full bg-red-600 items-center justify-center"
            >
              <IconX size={14} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );

  const renderFooter = () => {
    if (!hasMore) return null;

    return (
      <View className="py-4 items-center">
        <ActivityIndicator size="small" color="#2196F3" />
        <Text className="text-zinc-500 text-sm mt-2">Loading more...</Text>
      </View>
    );
  };

  const renderEmpty = () => (
    <View className="flex-1 items-center justify-center py-12">
      <View className="w-16 h-16 rounded-full bg-zinc-800 items-center justify-center mb-4">
        <IconBell size={24} color="#6b7280" />
      </View>
      <Text className="text-zinc-400 text-lg font-medium mb-2">
        No notifications yet
      </Text>
      <Text className="text-zinc-500 text-sm text-center px-8">
        When Allyson needs your help or has updates for you, they'll appear here.
      </Text>
    </View>
  );

  if (isLoading && notifications.length === 0) {
    return (
      <View className={`flex-1 items-center justify-center ${className}`}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text className="text-zinc-400 mt-4">Loading notifications...</Text>
      </View>
    );
  }

  return (
    <View className={`flex-1 ${className}`}>
      <FlatList
        data={notifications}
        renderItem={renderNotificationItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{
          padding: 16,
          flexGrow: 1,
        }}
        onEndReached={fetchMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={refetch}
            colors={['#2196F3']}
            tintColor="#2196F3"
          />
        }
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default NovuInbox;
