import React, { useState } from 'react';
import { View, ScrollView, Alert } from 'react-native';
import { Button } from '~/components/ui/button';
import { Text } from '~/components/ui/typography';
import { Card } from '~/components/ui/card';
import { novuNotificationService } from '~/lib/novuService';
import { useNovuNotifications } from '~/hooks/useNovuNotifications';
import { useAuth } from '@clerk/clerk-expo';
import Toast from 'react-native-toast-message';

/**
 * Test component for Novu notification functionality
 * Use this component to manually test notification features
 */
export const NotificationTester: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { setupNotifications, isInitialized, subscriberId } = useNovuNotifications();
  const { userId, getToken } = useAuth();

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testPermissions = async () => {
    setIsLoading(true);
    try {
      addTestResult('Testing permission status...');
      const status = await novuNotificationService.checkPermissionStatus();
      addTestResult(`Permission status: ${status.status}`);

      if (status.status !== 'granted') {
        addTestResult('Requesting permissions...');
        const requestResult = await novuNotificationService.requestPermissions();
        addTestResult(`Permission request result: ${requestResult.status}`);
      }
    } catch (error) {
      addTestResult(`Permission test failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testPushToken = async () => {
    setIsLoading(true);
    try {
      addTestResult('Getting push token...');
      const tokenData = await novuNotificationService.getPushToken();
      if (tokenData) {
        addTestResult(`Push token received: ${tokenData.type}`);
        addTestResult(`Token preview: ${tokenData.token.substring(0, 20)}...`);
      } else {
        addTestResult('Failed to get push token');
      }
    } catch (error) {
      addTestResult(`Push token test failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testDeviceRegistration = async () => {
    setIsLoading(true);
    try {
      if (!subscriberId) {
        addTestResult('No subscriber ID available');
        return;
      }

      addTestResult('Testing device registration...');
      const tokenData = await novuNotificationService.getPushToken();
      if (!tokenData) {
        addTestResult('Cannot register device: no push token');
        return;
      }

      const registered = await novuNotificationService.registerDevice(subscriberId, tokenData);
      addTestResult(`Device registration: ${registered ? 'SUCCESS' : 'FAILED'}`);
    } catch (error) {
      addTestResult(`Device registration test failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testNotificationSettings = async () => {
    setIsLoading(true);
    try {
      if (!userId) {
        addTestResult('No user ID available');
        return;
      }

      addTestResult('Testing notification settings update...');
      const token = await getToken();
      const tokenData = await novuNotificationService.getPushToken();

      const success = await novuNotificationService.updateNotificationSettings(token, {
        mobile: true,
        email: false,
        subscriberId: userId,
        pushToken: tokenData,
      });

      addTestResult(`Notification settings update: ${success ? 'SUCCESS' : 'FAILED'}`);
    } catch (error) {
      addTestResult(`Notification settings test failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testFullSetup = async () => {
    setIsLoading(true);
    try {
      addTestResult('Running full notification setup test...');
      const success = await setupNotifications();
      addTestResult(`Full setup: ${success ? 'SUCCESS' : 'FAILED'}`);
    } catch (error) {
      addTestResult(`Full setup test failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testNotificationPress = () => {
    addTestResult('Testing notification press handling...');
    const { handleNotificationPress } = useNovuNotifications();
    
    // Simulate a notification press with test data
    const testNotification = {
      request: {
        content: {
          data: {
            sessionId: 'test-session-123',
            url: '/(drawer)/(sessions)/session?id=test-session-123',
          }
        }
      }
    };

    try {
      handleNotificationPress(testNotification);
      addTestResult('Notification press test completed (check navigation)');
    } catch (error) {
      addTestResult(`Notification press test failed: ${error.message}`);
    }
  };

  return (
    <ScrollView className="flex-1 p-4">
      <Card className="p-4 mb-4">
        <Text className="text-lg font-semibold mb-2 text-zinc-200">
          Novu Notification Tester
        </Text>
        <Text className="text-sm text-zinc-400 mb-4">
          Use this component to test notification functionality
        </Text>
        
        <View className="mb-4">
          <Text className="text-sm text-zinc-300">
            Initialized: {isInitialized ? 'Yes' : 'No'}
          </Text>
          <Text className="text-sm text-zinc-300">
            Subscriber ID: {subscriberId || 'None'}
          </Text>
          <Text className="text-sm text-zinc-300">
            User ID: {userId || 'None'}
          </Text>
        </View>
      </Card>

      <Card className="p-4 mb-4">
        <Text className="text-md font-semibold mb-3 text-zinc-200">
          Test Functions
        </Text>
        
        <View className="space-y-2">
          <Button 
            onPress={testPermissions} 
            disabled={isLoading}
            className="mb-2"
          >
            <Text>Test Permissions</Text>
          </Button>
          
          <Button 
            onPress={testPushToken} 
            disabled={isLoading}
            className="mb-2"
          >
            <Text>Test Push Token</Text>
          </Button>
          
          <Button 
            onPress={testDeviceRegistration} 
            disabled={isLoading}
            className="mb-2"
          >
            <Text>Test Device Registration</Text>
          </Button>
          
          <Button 
            onPress={testNotificationSettings} 
            disabled={isLoading}
            className="mb-2"
          >
            <Text>Test Notification Settings</Text>
          </Button>
          
          <Button 
            onPress={testFullSetup} 
            disabled={isLoading}
            className="mb-2"
          >
            <Text>Test Full Setup</Text>
          </Button>
          
          <Button 
            onPress={testNotificationPress} 
            disabled={isLoading}
            className="mb-2"
          >
            <Text>Test Notification Press</Text>
          </Button>
        </View>
      </Card>

      <Card className="p-4 mb-4">
        <View className="flex-row justify-between items-center mb-3">
          <Text className="text-md font-semibold text-zinc-200">
            Test Results
          </Text>
          <Button 
            onPress={clearResults} 
            variant="outline"
            size="sm"
          >
            <Text>Clear</Text>
          </Button>
        </View>
        
        <ScrollView className="max-h-64">
          {testResults.length === 0 ? (
            <Text className="text-sm text-zinc-400 italic">
              No test results yet
            </Text>
          ) : (
            testResults.map((result, index) => (
              <Text 
                key={index} 
                className="text-xs text-zinc-300 mb-1 font-mono"
              >
                {result}
              </Text>
            ))
          )}
        </ScrollView>
      </Card>
    </ScrollView>
  );
};

export default NotificationTester;
