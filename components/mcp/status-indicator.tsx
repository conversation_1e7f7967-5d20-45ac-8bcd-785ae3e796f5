import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { useMCP } from '~/context/MCPContext';
import { Text } from '~/components/ui/typography';
import { Button } from '~/components/ui/button';
import { 
  IconWifi, 
  IconWifiOff, 
  IconLoader2, 
  IconAlertTriangle,
  IconBell,
  IconBellOff 
} from '@tabler/icons-react-native';
import * as Haptics from 'expo-haptics';

interface MCPStatusIndicatorProps {
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const MCPStatusIndicator: React.FC<MCPStatusIndicatorProps> = ({ 
  showText = true, 
  size = 'md',
  className = ''
}) => {
  const { isConnected, connectionStatus, lastError, currentDialog, connect, disconnect } = useMCP();

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return currentDialog ? '#f59e0b' : '#10b981'; // yellow if dialog pending, green if connected
      case 'connecting':
        return '#6b7280'; // gray
      case 'error':
        return '#ef4444'; // red
      case 'disconnected':
      default:
        return '#6b7280'; // gray
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return currentDialog ? 'Action Required' : 'MCP Connected';
      case 'connecting':
        return 'Connecting...';
      case 'error':
        return lastError || 'Connection Error';
      case 'disconnected':
      default:
        return 'MCP Disconnected';
    }
  };

  const getStatusIcon = () => {
    const iconSize = size === 'sm' ? 16 : size === 'lg' ? 24 : 20;
    const color = getStatusColor();

    switch (connectionStatus) {
      case 'connected':
        return currentDialog ? 
          <IconBell size={iconSize} color={color} /> : 
          <IconWifi size={iconSize} color={color} />;
      case 'connecting':
        return <IconLoader2 size={iconSize} color={color} className="animate-spin" />;
      case 'error':
        return <IconAlertTriangle size={iconSize} color={color} />;
      case 'disconnected':
      default:
        return <IconWifiOff size={iconSize} color={color} />;
    }
  };

  const handlePress = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    if (connectionStatus === 'disconnected' || connectionStatus === 'error') {
      connect();
    }
  };

  const sizeClasses = {
    sm: 'h-6 px-2',
    md: 'h-8 px-3',
    lg: 'h-10 px-4'
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      disabled={connectionStatus === 'connecting' || connectionStatus === 'connected'}
      className={`flex-row items-center gap-2 rounded-full border ${sizeClasses[size]} ${
        currentDialog ? 'border-yellow-500 bg-yellow-500/10' :
        isConnected ? 'border-green-500 bg-green-500/10' :
        connectionStatus === 'error' ? 'border-red-500 bg-red-500/10' :
        'border-gray-500 bg-gray-500/10'
      } ${className}`}
    >
      {getStatusIcon()}
      {showText && (
        <Text 
          className={`text-xs font-medium ${
            currentDialog ? 'text-yellow-400' :
            isConnected ? 'text-green-400' :
            connectionStatus === 'error' ? 'text-red-400' :
            'text-gray-400'
          }`}
        >
          {getStatusText()}
        </Text>
      )}
    </TouchableOpacity>
  );
};

interface MCPConnectionManagerProps {
  className?: string;
}

export const MCPConnectionManager: React.FC<MCPConnectionManagerProps> = ({ className }) => {
  const { 
    isConnected, 
    connectionStatus, 
    lastError, 
    currentDialog,
    connect, 
    disconnect 
  } = useMCP();

  const handleToggleConnection = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    if (isConnected) {
      disconnect();
    } else {
      connect();
    }
  };

  return (
    <View className={`p-4 rounded-lg border border-border bg-card ${className}`}>
      <View className="flex-row items-center justify-between mb-4">
        <Text className="text-lg font-semibold">MCP Server</Text>
        <MCPStatusIndicator size="sm" />
      </View>

      <View className="space-y-3">
        <View className="flex-row items-center justify-between">
          <Text className="text-sm text-muted-foreground">Status</Text>
          <Text className={`text-sm font-medium ${
            isConnected ? 'text-green-400' : 'text-gray-400'
          }`}>
            {getStatusText()}
          </Text>
        </View>

        {currentDialog && (
          <View className="flex-row items-center justify-between">
            <Text className="text-sm text-muted-foreground">Pending Dialog</Text>
            <Text className="text-sm font-medium text-yellow-400">
              {currentDialog.type.charAt(0).toUpperCase() + currentDialog.type.slice(1)}
            </Text>
          </View>
        )}

        {lastError && (
          <View className="p-3 rounded-lg bg-red-500/10 border border-red-500/20">
            <Text className="text-sm text-red-400">{lastError}</Text>
          </View>
        )}

        <Button
          onPress={handleToggleConnection}
          variant={isConnected ? "destructive" : "default"}
          disabled={connectionStatus === 'connecting'}
          className="w-full"
        >
          <Text>
            {connectionStatus === 'connecting' ? 'Connecting...' :
             isConnected ? 'Disconnect' : 'Connect'}
          </Text>
        </Button>
      </View>
    </View>
  );

  function getStatusText() {
    switch (connectionStatus) {
      case 'connected':
        return currentDialog ? 'Action Required' : 'Connected';
      case 'connecting':
        return 'Connecting...';
      case 'error':
        return 'Error';
      case 'disconnected':
      default:
        return 'Disconnected';
    }
  }
};