import * as React from 'react';
import { create } from 'zustand';
const DEFAULT_PORTAL_HOST = 'INTERNAL_PRIMITIVE_DEFAULT_HOST_NAME';
const usePortal = create(() => ({
    map: new Map().set(DEFAULT_PORTAL_HOST, new Map()),
}));
const updatePortal = (hostName, name, children) => {
    usePortal.setState((prev) => {
        const next = new Map(prev.map);
        const portal = next.get(hostName) ?? new Map();
        portal.set(name, children);
        next.set(hostName, portal);
        return { map: next };
    });
};
const removePortal = (hostName, name) => {
    usePortal.setState((prev) => {
        const next = new Map(prev.map);
        const portal = next.get(hostName) ?? new Map();
        portal.delete(name);
        next.set(hostName, portal);
        return { map: next };
    });
};
export function PortalHost({ name = DEFAULT_PORTAL_HOST }) {
    const portalMap = usePortal((state) => state.map).get(name) ??
        new Map();
    if (portalMap.size === 0)
        return null;
    return <>{Array.from(portalMap.values())}</>;
}
export function Portal({ name, hostName = DEFAULT_PORTAL_HOST, children, }) {
    React.useEffect(() => {
        updatePortal(hostName, name, children);
    }, [hostName, name, children]);
    React.useEffect(() => {
        return () => {
            removePortal(hostName, name);
        };
    }, [hostName, name]);
    return null;
}
