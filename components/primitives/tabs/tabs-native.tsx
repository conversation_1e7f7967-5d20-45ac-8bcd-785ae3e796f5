import * as React from 'react';
import { Pressable, View } from 'react-native';
import * as Slot from '~/components/primitives/slot';
const TabsContext = React.createContext(null);
const Root = React.forwardRef(({ asChild, value, onValueChange, orientation: _orientation, dir: _dir, activationMode: _activationMode, ...viewProps }, ref) => {
    const nativeID = React.useId();
    const Component = asChild ? Slot.View : View;
    return (<TabsContext.Provider value={{
            value,
            onValueChange,
            nativeID,
        }}>
        <Component ref={ref} {...viewProps}/>
      </TabsContext.Provider>);
});
Root.displayName = 'RootNativeTabs';
function useTabsContext() {
    const context = React.useContext(TabsContext);
    if (!context) {
        throw new Error('Tabs compound components cannot be rendered outside the Tabs component');
    }
    return context;
}
const List = React.forwardRef(({ asChild, ...props }, ref) => {
    const Component = asChild ? Slot.View : View;
    return <Component ref={ref} role='tablist' {...props}/>;
});
List.displayName = 'ListNativeTabs';
const TriggerContext = React.createContext(null);
const Trigger = React.forwardRef(({ asChild, onPress: onPressProp, disabled, value: tabValue, ...props }, ref) => {
    const { onValueChange, value: rootValue, nativeID } = useTabsContext();
    function onPress(ev) {
        if (disabled)
            return;
        onValueChange(tabValue);
        onPressProp?.(ev);
    }
    const Component = asChild ? Slot.Pressable : Pressable;
    return (<TriggerContext.Provider value={{ value: tabValue }}>
        <Component ref={ref} nativeID={`${nativeID}-tab-${tabValue}`} aria-disabled={!!disabled} aria-selected={rootValue === tabValue} role='tab' onPress={onPress} accessibilityState={{
            selected: rootValue === tabValue,
            disabled: !!disabled,
        }} disabled={!!disabled} {...props}/>
      </TriggerContext.Provider>);
});
Trigger.displayName = 'TriggerNativeTabs';
function useTriggerContext() {
    const context = React.useContext(TriggerContext);
    if (!context) {
        throw new Error('Tabs.Trigger compound components cannot be rendered outside the Tabs.Trigger component');
    }
    return context;
}
const Content = React.forwardRef(({ asChild, forceMount, value: tabValue, ...props }, ref) => {
    const { value: rootValue, nativeID } = useTabsContext();
    if (!forceMount) {
        if (rootValue !== tabValue) {
            return null;
        }
    }
    const Component = asChild ? Slot.View : View;
    return (<Component ref={ref} aria-hidden={!(forceMount || rootValue === tabValue)} aria-labelledby={`${nativeID}-tab-${tabValue}`} role='tabpanel' {...props}/>);
});
Content.displayName = 'ContentNativeTabs';
export { Content, List, Root, Trigger, useTabsContext, useTriggerContext };
