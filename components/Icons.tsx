import {
  Activity,
  Send,
  Trash,
  Speech,
  Save,
  Eye,
  Pencil,
  Airplay,
  AlarmClockIcon,
  AlertCircle,
  AlignJustify,
  AlignLeft,
  AlignRight,
  ArrowDown,
  ArrowUp,
  Baby,
  BadgeAlert,
  Bell,
  BellDot,
  Bold,
  Calendar,
  CalendarDays,
  Cog,
  Check,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  ChevronUp,
  ChevronsDownUp,
  ChevronsUpDown,
  Copy,
  Database,
  Ear,
  Fan,
  CircleUserRound,
  GalleryHorizontal,
  Info,
  Italic,
  Lamp,
  LayoutPanelLeft,
  Menu,
  MoonStar,
  Search,
  Sparkles,
  Sun,
  Table,
  Underline,
  X,
  Cloud,
  Github,
  LifeBuoy,
  LogOut,
  Mail,
  MessageSquare,
  Plus,
  PlusCircle,
  UserPlus,
  Users,
  AlignCenter,
  Pause,
  Play,
} from "lucide-react-native";
import { cssInterop } from "nativewind";
function interopIcon(icon) {
  cssInterop(icon, {
    className: {
      target: "style",
      nativeStyleToProp: {
        color: true,
        opacity: true,
      },
    },
  });
}
interopIcon(Bell);
interopIcon(Pencil);
interopIcon(Pause);
interopIcon(Play);
interopIcon(Send);
interopIcon(Save);
interopIcon(Eye);
interopIcon(Trash);
interopIcon(Cog);
interopIcon(BellDot);
interopIcon(Sun);
interopIcon(MoonStar);
interopIcon(AlignJustify);
interopIcon(Activity);
interopIcon(Airplay);
interopIcon(AlarmClockIcon);
interopIcon(AlertCircle);
interopIcon(AlignRight);
interopIcon(Baby);
interopIcon(BadgeAlert);
interopIcon(Calendar);
interopIcon(CircleUserRound);
interopIcon(Database);
interopIcon(Speech);
interopIcon(Ear);
interopIcon(Fan);
interopIcon(GalleryHorizontal);
interopIcon(Lamp);
interopIcon(Table);
interopIcon(ArrowDown);
interopIcon(ArrowUp);
interopIcon(ChevronDown);
interopIcon(LayoutPanelLeft);
interopIcon(Menu);
interopIcon(Check);
interopIcon(ChevronsDownUp);
interopIcon(ChevronsUpDown);
interopIcon(Copy);
interopIcon(CalendarDays);
interopIcon(Sparkles);
interopIcon(Bold);
interopIcon(Italic);
interopIcon(Underline);
interopIcon(AlignLeft);
interopIcon(Info);
interopIcon(X);
interopIcon(Search);
interopIcon(ChevronRight);
interopIcon(ChevronLeft);
interopIcon(ChevronUp);
interopIcon(Cloud);
interopIcon(Github);
interopIcon(LifeBuoy);
interopIcon(LogOut);
interopIcon(Mail);
interopIcon(MessageSquare);
interopIcon(Plus);
interopIcon(PlusCircle);
interopIcon(UserPlus);
interopIcon(Users);
interopIcon(AlignCenter);
export {
  Activity,
  Airplay,
  Bell,
  Trash,
  Pencil,
  Save,
  Send,
  Cog,
  BellDot,
  AlarmClockIcon,
  AlertCircle,
  AlignJustify,
  AlignLeft,
  AlignRight,
  ArrowDown,
  ArrowUp,
  Baby,
  BadgeAlert,
  Bold,
  Calendar,
  CalendarDays,
  Check,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  ChevronUp,
  ChevronsDownUp,
  ChevronsUpDown,
  Copy,
  CircleUserRound,
  Database,
  Ear,
  Fan,
  GalleryHorizontal,
  Info,
  Italic,
  Lamp,
  LayoutPanelLeft,
  Menu,
  MoonStar,
  Search,
  Sparkles,
  Sun,
  Table,
  Underline,
  X,
  Cloud,
  Github,
  LifeBuoy,
  LogOut,
  Mail,
  MessageSquare,
  Plus,
  PlusCircle,
  UserPlus,
  Users,
  AlignCenter,
  Eye,
  Speech,
  Pause,
  Play,
};
