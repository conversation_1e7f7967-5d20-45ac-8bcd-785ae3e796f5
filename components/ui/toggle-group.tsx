import * as LucideIcon from 'lucide-react-native';
import * as React from 'react';
import { toggleTextVariants, toggleVariants } from '~/components/ui/toggle';
import { TextClassContext } from '~/components/ui/typography';
import * as ToggleGroupPrimitive from '~/components/primitives/toggle-group';
import { cn } from '~/lib/utils';
const ToggleGroupContext = React.createContext(null);
const ToggleGroup = React.forwardRef(({ className, variant, size, children, ...props }, ref) => (<ToggleGroupPrimitive.Root ref={ref} className={cn('flex flex-row items-center justify-center gap-1', className)} {...props}>
    <ToggleGroupContext.Provider value={{ variant, size }}>
      {children}
    </ToggleGroupContext.Provider>
  </ToggleGroupPrimitive.Root>));
ToggleGroup.displayName = ToggleGroupPrimitive.Root.displayName;
function useToggleGroupContext() {
    const context = React.useContext(ToggleGroupContext);
    if (context === null) {
        throw new Error('ToggleGroup compound components cannot be rendered outside the ToggleGroup component');
    }
    return context;
}
const ToggleGroupItem = React.forwardRef(({ className, children, variant, size, ...props }, ref) => {
    const context = useToggleGroupContext();
    const { value } = ToggleGroupPrimitive.useRootContext();
    return (<TextClassContext.Provider value={cn(toggleTextVariants({ variant, size }), ToggleGroupPrimitive.utils.getIsSelected(value, props.value)
            ? 'text-accent-foreground'
            : 'web:group-hover:text-muted-foreground')}>
      <ToggleGroupPrimitive.Item ref={ref} className={cn(toggleVariants({
            variant: context.variant || variant,
            size: context.size || size,
        }), props.disabled && 'web:pointer-events-none opacity-50', ToggleGroupPrimitive.utils.getIsSelected(value, props.value) &&
            'bg-accent', className)} {...props}>
        {children}
      </ToggleGroupPrimitive.Item>
    </TextClassContext.Provider>);
});
ToggleGroupItem.displayName = ToggleGroupPrimitive.Item.displayName;
function ToggleGroupIcon({ className, name, ...props }) {
    const textClass = React.useContext(TextClassContext);
    const Icon = LucideIcon[name];
    return <Icon className={cn(textClass, className)} {...props}/>;
}
export { ToggleGroup, ToggleGroupIcon, ToggleGroupItem };
