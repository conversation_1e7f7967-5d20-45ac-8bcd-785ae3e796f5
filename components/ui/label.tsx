import { cva } from 'class-variance-authority';
import * as React from 'react';
import * as LabelPrimitive from '~/components/primitives/label';
import { cn } from '~/lib/utils';
const labelVariants = cva('text-sm text-foreground native:text-base font-medium leading-none web:peer-disabled:cursor-not-allowed web:peer-disabled:opacity-70');
const Label = React.forwardRef(({ className, ...props }, ref) => (<LabelPrimitive.Root ref={ref} className={cn('web:cursor-default', className)} {...props}/>));
Label.displayName = LabelPrimitive.Root.displayName;
const LabelText = React.forwardRef(({ className, ...props }, ref) => (<LabelPrimitive.Text ref={ref} className={cn(labelVariants(), className)} {...props}/>));
LabelText.displayName = LabelPrimitive.Text.displayName;
export { Label, LabelText };
