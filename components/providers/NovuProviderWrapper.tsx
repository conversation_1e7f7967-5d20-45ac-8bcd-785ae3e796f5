import React from 'react';
import { NovuProvider } from '@novu/react-native';
import { useAuth } from '@clerk/clerk-expo';
import { useUser } from '~/context/UserContext';

interface NovuProviderWrapperProps {
  children: React.ReactNode;
}

/**
 * Wrapper component for NovuProvider that uses user context for subscriber ID
 */
export const NovuProviderWrapper: React.FC<NovuProviderWrapperProps> = ({ children }) => {
  const { userId } = useAuth();
  const { user } = useUser();

  // Determine subscriber ID from available user data
  const subscriberId = userId || user?.id || 'anonymous-user';

  return (
    <NovuProvider
      applicationIdentifier={process.env.EXPO_PUBLIC_NOVU_APP_ID || ""}
      subscriberId={subscriberId}
    >
      {children}
    </NovuProvider>
  );
};

export default NovuProviderWrapper;
