import React, { useState, useEffect } from "react";
import { View, Platform, Pressable } from "react-native";
import { useAuth } from "@clerk/clerk-expo";
import * as Device from "expo-device";
import Toast from "react-native-toast-message";
import { <PERSON><PERSON>, DialogContent, DialogHeader } from "~/components/ui/dialog";
import { Text } from "~/components/ui/typography";
import { Card } from "~/components/ui/card";
import { Switch } from "~/components/ui/switch";
import { IconX } from "@tabler/icons-react-native";
import * as Haptics from "expo-haptics";
import { useUser } from "~/context/UserContext";
import { useMCP } from "~/context/MCPContext";
import { novuNotificationService } from "~/lib/novuService";

export default function NotificationsDialog({ isModalOpen, setIsModalOpen }) {
  const { getToken } = useAuth();
  const { user } = useUser();
  const { isConnected } = useMCP();
  const [notificationSettings, setNotificationSettings] = useState({});
  const [mcpNotifications, setMcpNotifications] = useState(true);

  async function registerForPushNotificationsAsync() {
    try {
      // Check permissions first
      const permissionStatus = await novuNotificationService.checkPermissionStatus();

      if (permissionStatus.status !== 'granted') {
        const requestResult = await novuNotificationService.requestPermissions();
        if (requestResult.status !== 'granted') {
          Toast.show({
            type: 'error',
            text1: 'Permission Required',
            text2: 'Go to Settings > Notifications > Allyson and enable the notifications and then go back to Allyson and toggle the switch for "Mobile Notifications" in the app settings.',
          });
          return null;
        }
      }

      // Get push token
      const pushToken = await novuNotificationService.getPushToken();
      if (!pushToken) {
        throw new Error('Failed to get push token');
      }

      return pushToken;
    } catch (error) {
      console.error('Error registering for push notifications:', error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to setup push notifications. Please try again.",
      });
      return null;
    }
  }

  const handleToggleChange = async (settingName, newValue) => {
    // Update local state
    const updatedSettings = {
      ...notificationSettings,
      [settingName]: newValue,
    };
    setNotificationSettings(updatedSettings);

    // If the mobile notifications setting is toggled to true, register for push notifications
    if (settingName === "mobile" && newValue === true) {
      try {
        const pushToken = await registerForPushNotificationsAsync();
        if (pushToken) {
          // Register device with Novu
          const subscriberId = user?.id || 'unknown';
          const registered = await novuNotificationService.registerDevice(subscriberId, pushToken);

          if (registered) {
            // Send updated settings and push token to the backend
            updateNotificationSettingsBackend(
              updatedSettings.mobile,
              updatedSettings.email,
              pushToken
            );
          } else {
            Toast.show({
              type: "error",
              text1: "Error",
              text2: "Failed to register device. Please try again.",
            });
          }
        }
      } catch (error) {
        console.error("Error registering for push notifications:", error);
        // Handle any errors, such as reverting the toggle or showing an error message
      }
    } else {
      // For other cases, just send the updated settings without the push token
      updateNotificationSettingsBackend(
        updatedSettings.mobile,
        updatedSettings.email,
        null
      );
    }
  };

  async function updateNotificationSettingsBackend(
    mobile,
    email,
    pushToken
  ) {
    try {
      const token = await getToken();
      const success = await novuNotificationService.updateNotificationSettings(token, {
        mobile: mobile,
        email: email,
        subscriberId: user?.id || 'unknown',
        pushToken: pushToken || undefined,
      });

      if (!success) {
        Toast.show({
          type: "error",
          text1: "Error",
          text2: "Please Try Again.",
        });
      }
    } catch (error) {
      console.log("update notification settings error: ", error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Please Try Again.",
      });
    }
  }

  useEffect(() => {
    if (user) {
      setNotificationSettings({
        email: user.notificationSettings?.email,
        mobile: user.notificationSettings?.mobile,
      });
    }
  }, [user]);

  return (
    <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
      <DialogContent className="my-auto">
        <DialogHeader>
          <View className="flex-row justify-end items-end w-full">
            <Pressable
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setIsModalOpen(false);
              }}
            >
              <IconX size={20} color="#a1a1aa" />
            </Pressable>
          </View>
          <Text className="text-xl text-center font-semibold text-zinc-200">
            Notification Settings
          </Text>
          <Text className="text-md text-center text-zinc-400">
            Allyson will need your help with tasks and the best way to get ahold
            of you is via notifications. Please enable them to get the most out
            of Allyson.
          </Text>
        </DialogHeader>
        <View className=" rounded-xl p-1 mt-10">
          <Card className="w-full rounded-xl p-5 flex flex-row items-center justify-between">
            <View className="flex flex-col">
              <Text className="text-zinc-400 font-bold">
                Mobile Notifications
              </Text>
            </View>
            <Switch
              onCheckedChange={(newValue) =>
                handleToggleChange("mobile", newValue)
              }
              checked={notificationSettings.mobile}
            />
          </Card>
          <Card className="mt-5 p-5 w-full flex flex-row items-center justify-between">
            <View className="flex flex-col">
              <Text className="text-zinc-400 font-bold">
                Email Notifications
              </Text>
            </View>
            <Switch
              onCheckedChange={(newValue) =>
                handleToggleChange("email", newValue)
              }
              checked={notificationSettings.email}
            />
          </Card>
          <Card className="mt-5 p-5 w-full flex flex-row items-center justify-between">
            <View className="flex flex-col">
              <Text className="text-zinc-400 font-bold">
                MCP Dialog Alerts
              </Text>
              <Text className="text-zinc-500 text-xs mt-1">
                Human-in-the-loop dialog requests{isConnected ? ' (Connected)' : ' (Disconnected)'}
              </Text>
            </View>
            <Switch
              onCheckedChange={setMcpNotifications}
              checked={mcpNotifications}
            />
          </Card>
        </View>
      </DialogContent>
    </Dialog>
  );
}
