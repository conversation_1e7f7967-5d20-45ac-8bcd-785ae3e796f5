import React, { useState, useEffect } from 'react';
import { View, ScrollView, TouchableOpacity } from 'react-native';
import { useMCP, MCPDialog, MCPResponse } from '~/context/MCPContext';
import { Text } from '~/components/ui/typography';
import { Button } from '~/components/ui/button';
import { DynamicTextInput } from '~/components/ui/input';
import { Card } from '~/components/ui/card';
import { Checkbox } from '~/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '~/components/ui/radio-group';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '~/components/ui/dialog';
import * as Haptics from 'expo-haptics';

interface MCPDialogManagerProps {
  className?: string;
}

export const MCPDialogManager: React.FC<MCPDialogManagerProps> = ({ className }) => {
  const { currentDialog, respondToDialog } = useMCP();
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (currentDialog) {
      setIsOpen(true);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
    } else {
      setIsOpen(false);
    }
  }, [currentDialog]);

  const handleResponse = (success: boolean, data?: any, error?: string) => {
    if (!currentDialog) return;

    const response: MCPResponse = {
      id: currentDialog.id,
      success,
      data,
      error
    };

    respondToDialog(response);
    setIsOpen(false);
  };

  if (!currentDialog) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        handleResponse(false, null, 'Dialog cancelled by user');
      }
    }}>
      <DialogContent className={`max-w-md ${className}`}>
        <DialogHeader>
          <DialogTitle className="text-foreground">
            {currentDialog.title}
          </DialogTitle>
          <DialogDescription className="text-muted-foreground">
            {currentDialog.message}
          </DialogDescription>
        </DialogHeader>

        {currentDialog.type === 'input' && (
          <MCPInputDialog dialog={currentDialog} onResponse={handleResponse} />
        )}

        {currentDialog.type === 'choice' && (
          <MCPChoiceDialog dialog={currentDialog} onResponse={handleResponse} />
        )}

        {currentDialog.type === 'multiline' && (
          <MCPMultilineDialog dialog={currentDialog} onResponse={handleResponse} />
        )}

        {currentDialog.type === 'confirmation' && (
          <MCPConfirmationDialog dialog={currentDialog} onResponse={handleResponse} />
        )}

        {currentDialog.type === 'info' && (
          <MCPInfoDialog dialog={currentDialog} onResponse={handleResponse} />
        )}
      </DialogContent>
    </Dialog>
  );
};

interface DialogComponentProps {
  dialog: MCPDialog;
  onResponse: (success: boolean, data?: any, error?: string) => void;
}

const MCPInputDialog: React.FC<DialogComponentProps> = ({ dialog, onResponse }) => {
  const [value, setValue] = useState(dialog.options?.defaultValue || '');

  const handleSubmit = () => {
    if (!value.trim()) {
      onResponse(false, null, 'Input cannot be empty');
      return;
    }
    onResponse(true, { value: value.trim() });
  };

  return (
    <View className="space-y-4">
      <DynamicTextInput
        value={value}
        onChangeText={setValue}
        placeholder="Enter your response..."
        keyboardType={
          dialog.options?.inputType === 'email' ? 'email-address' :
          dialog.options?.inputType === 'number' ? 'numeric' : 'default'
        }
        secureTextEntry={dialog.options?.inputType === 'password'}
        autoFocus
      />
      <DialogFooter className="flex-row gap-2">
        <Button
          variant="outline"
          onPress={() => onResponse(false, null, 'Cancelled by user')}
          className="flex-1"
        >
          <Text>Cancel</Text>
        </Button>
        <Button onPress={handleSubmit} className="flex-1">
          <Text>Submit</Text>
        </Button>
      </DialogFooter>
    </View>
  );
};

const MCPChoiceDialog: React.FC<DialogComponentProps> = ({ dialog, onResponse }) => {
  const [selectedChoices, setSelectedChoices] = useState<string[]>([]);
  const [selectedSingle, setSelectedSingle] = useState<string>('');
  const choices = dialog.options?.choices || [];
  const allowMultiple = dialog.options?.allowMultiple || false;

  const handleChoiceToggle = (choice: string) => {
    if (allowMultiple) {
      setSelectedChoices(prev => 
        prev.includes(choice) 
          ? prev.filter(c => c !== choice)
          : [...prev, choice]
      );
    } else {
      setSelectedSingle(choice);
    }
  };

  const handleSubmit = () => {
    const selected = allowMultiple ? selectedChoices : [selectedSingle];
    if (selected.length === 0 || (selected.length === 1 && !selected[0])) {
      onResponse(false, null, 'Please make a selection');
      return;
    }
    onResponse(true, { choices: selected });
  };

  return (
    <View className="space-y-4">
      <ScrollView className="max-h-64" showsVerticalScrollIndicator={false}>
        {allowMultiple ? (
          <View className="space-y-2">
            {choices.map((choice, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => handleChoiceToggle(choice)}
                className="flex-row items-center space-x-3 p-3 rounded-lg border border-border"
              >
                <Checkbox
                  checked={selectedChoices.includes(choice)}
                  onCheckedChange={() => handleChoiceToggle(choice)}
                />
                <Text className="flex-1">{choice}</Text>
              </TouchableOpacity>
            ))}
          </View>
        ) : (
          <RadioGroup value={selectedSingle} onValueChange={setSelectedSingle}>
            {choices.map((choice, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => setSelectedSingle(choice)}
                className="flex-row items-center space-x-3 p-3 rounded-lg border border-border"
              >
                <RadioGroupItem value={choice} />
                <Text className="flex-1">{choice}</Text>
              </TouchableOpacity>
            ))}
          </RadioGroup>
        )}
      </ScrollView>
      <DialogFooter className="flex-row gap-2">
        <Button
          variant="outline"
          onPress={() => onResponse(false, null, 'Cancelled by user')}
          className="flex-1"
        >
          <Text>Cancel</Text>
        </Button>
        <Button onPress={handleSubmit} className="flex-1">
          <Text>Submit</Text>
        </Button>
      </DialogFooter>
    </View>
  );
};

const MCPMultilineDialog: React.FC<DialogComponentProps> = ({ dialog, onResponse }) => {
  const [value, setValue] = useState(dialog.options?.defaultValue || '');

  const handleSubmit = () => {
    if (!value.trim()) {
      onResponse(false, null, 'Input cannot be empty');
      return;
    }
    onResponse(true, { value: value.trim() });
  };

  return (
    <View className="space-y-4">
      <DynamicTextInput
        value={value}
        onChangeText={setValue}
        placeholder="Enter your response..."
        multiline
        numberOfLines={6}
        textAlignVertical="top"
        autoFocus
      />
      <DialogFooter className="flex-row gap-2">
        <Button
          variant="outline"
          onPress={() => onResponse(false, null, 'Cancelled by user')}
          className="flex-1"
        >
          <Text>Cancel</Text>
        </Button>
        <Button onPress={handleSubmit} className="flex-1">
          <Text>Submit</Text>
        </Button>
      </DialogFooter>
    </View>
  );
};

const MCPConfirmationDialog: React.FC<DialogComponentProps> = ({ dialog, onResponse }) => {
  const confirmText = dialog.options?.confirmText || 'Confirm';
  const cancelText = dialog.options?.cancelText || 'Cancel';
  const variant = dialog.options?.variant || 'default';

  return (
    <DialogFooter className="flex-row gap-2">
      <Button
        variant="outline"
        onPress={() => onResponse(false, { confirmed: false })}
        className="flex-1"
      >
        <Text>{cancelText}</Text>
      </Button>
      <Button
        variant={variant === 'destructive' ? 'destructive' : 'default'}
        onPress={() => onResponse(true, { confirmed: true })}
        className="flex-1"
      >
        <Text>{confirmText}</Text>
      </Button>
    </DialogFooter>
  );
};

const MCPInfoDialog: React.FC<DialogComponentProps> = ({ dialog, onResponse }) => {
  const buttonText = dialog.options?.buttonText || 'OK';

  return (
    <DialogFooter>
      <Button
        onPress={() => onResponse(true, { acknowledged: true })}
        className="w-full"
      >
        <Text>{buttonText}</Text>
      </Button>
    </DialogFooter>
  );
};