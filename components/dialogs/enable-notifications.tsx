import React, { useState, useEffect } from "react";
import { View, Pressable } from "react-native";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
} from "~/components/ui/dialog";
import { Text } from "~/components/ui/typography";
import { useAuth } from "@clerk/clerk-expo";
import { IconX } from "@tabler/icons-react-native";
import { Button } from "../ui/button";
import * as Haptics from "expo-haptics";
import { useUser } from "~/context/UserContext";
import Toast from "react-native-toast-message";
import { novuNotificationService } from "~/lib/novuService";

export default function EnableNotificationsDialog({ isModalOpen, setIsModalOpen }) {
  const [permissionStatus, setPermissionStatus] = useState(null);
  const { user } = useUser();
  const { isLoaded, userId, getToken } = useAuth();

  useEffect(() => {
    checkNotificationPermissions();
  }, []);

  const checkNotificationPermissions = async () => {
    const status = await novuNotificationService.checkPermissionStatus();
    setPermissionStatus(status.status);
  };

  const requestNotificationPermissions = async () => {
    const result = await novuNotificationService.requestPermissions();
    setPermissionStatus(result.status);
    return result.status;
  };

  const getPushToken = async () => {
    const token = await novuNotificationService.getPushToken();
    return token;
  };

  const updateNotificationSettings = async (token, pushToken) => {
    try {
      const success = await novuNotificationService.updateNotificationSettings(token, {
        mobile: true,
        email: user?.notificationSettings?.email || false,
        subscriberId: userId,
        pushToken: pushToken || undefined,
      });

      if (success) {
        setIsModalOpen(false);
        Toast.show({
          type: "success",
          text1: "Notifications enabled",
          text2: "You will now receive notifications from Allyson via Novu.",
        });
      }

      return success;
    } catch (error) {
      console.error("Error updating notification settings:", error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to enable notifications. Please try again.",
      });
      return false;
    }
  };

  const handleContinue = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    const token = await getToken();

    try {
      if (permissionStatus !== "granted") {
        const status = await requestNotificationPermissions();
        if (status !== "granted") {
          Toast.show({
            type: "error",
            text1: "Permission Required",
            text2: "To receive notifications, please enable them in your device settings.",
          });
          return;
        }
      }

      // Get push token
      const pushToken = await getPushToken();
      if (!pushToken) {
        Toast.show({
          type: "error",
          text1: "Error",
          text2: "Failed to setup push notifications. Please try again.",
        });
        return;
      }

      // Register device with Novu
      const subscriberId = userId || 'unknown';
      const registered = await novuNotificationService.registerDevice(subscriberId, pushToken);

      if (!registered) {
        Toast.show({
          type: "error",
          text1: "Error",
          text2: "Failed to register device. Please try again.",
        });
        return;
      }

      // Update notification settings
      await updateNotificationSettings(token, pushToken);
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error enabling notifications:', error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "An unexpected error occurred. Please try again.",
      });
    }
  };

  return (
    <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
      <DialogContent className="my-auto">
        <DialogHeader>
          <View className="flex-row justify-end items-end w-full">
            <Pressable
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setIsModalOpen(false);
              }}
            >
              <IconX size={20} color="#a1a1aa" />
            </Pressable>
          </View>
          <Text className="text-xl text-center font-semibold text-zinc-200">
            You need to enable notifications
          </Text>
          <Text className="text-md text-center text-zinc-400">
            Allyson will need your help with tasks and the best way to get ahold
            of you is via notifications. Please enable them to get the most out
            of Allyson.
          </Text>
        </DialogHeader>
        <Button
          variant="outline"
          className="w-full flex-row items-center justify-center gap-2 mt-4"
          onPress={handleContinue}
        >
          <Text className="text-sm text-zinc-400">Enable Notifications</Text>
        </Button>
      </DialogContent>
    </Dialog>
  );
}
