import React, { useState, useEffect } from "react";
import { View, Pressable, Alert } from "react-native";
import {
  Dialog,
  DialogContent,
  DialogHeader,
} from "~/components/ui/dialog";
import { Text } from "~/components/ui/typography";
import { useAuth } from "@clerk/clerk-expo";
import { IconX } from "@tabler/icons-react-native";
import { Button } from "../ui/button";
import * as Haptics from "expo-haptics";
import { useUser } from "~/context/UserContext";
import Toast from "react-native-toast-message";
import { novuNotificationService } from "~/lib/novuService";

export default function NovuEnableNotificationsDialog({ isModalOpen, setIsModalOpen }) {
  const [permissionStatus, setPermissionStatus] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useUser();
  const { isLoaded, userId, getToken } = useAuth();

  useEffect(() => {
    checkNotificationPermissions();
  }, []);

  const checkNotificationPermissions = async () => {
    try {
      const status = await novuNotificationService.checkPermissionStatus();
      setPermissionStatus(status.status);
    } catch (error) {
      console.error('Error checking notification permissions:', error);
      setPermissionStatus('denied');
    }
  };

  const requestNotificationPermissions = async () => {
    try {
      const result = await novuNotificationService.requestPermissions();
      setPermissionStatus(result.status);
      return result.status;
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      setPermissionStatus('denied');
      return 'denied';
    }
  };

  const getPushToken = async () => {
    try {
      const pushToken = await novuNotificationService.getPushToken();
      return pushToken;
    } catch (error) {
      console.error('Error getting push token:', error);
      return null;
    }
  };

  const updateNotificationSettings = async (token, pushToken) => {
    try {
      const success = await novuNotificationService.updateNotificationSettings(token, {
        mobile: true,
        email: user?.notificationSettings?.email || false,
        subscriberId: userId,
        pushToken: pushToken || undefined,
      });

      if (success) {
        setIsModalOpen(false);
        Toast.show({
          type: "success",
          text1: "Notifications enabled",
          text2: "You will now receive notifications from Allyson via Novu.",
        });
      }

      return success;
    } catch (error) {
      console.error("Error updating notification settings:", error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Failed to enable notifications. Please try again.",
      });
      return false;
    }
  };

  const handleContinue = async () => {
    try {
      setIsLoading(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      const token = await getToken();

      // Check and request permissions if needed
      if (permissionStatus !== "granted") {
        const status = await requestNotificationPermissions();
        if (status !== "granted") {
          Alert.alert(
            "Permission Required",
            "To receive notifications, please enable them in your device settings.",
            [{ text: "OK" }]
          );
          return;
        }
      }

      // Get push token
      const pushToken = await getPushToken();
      if (!pushToken) {
        Toast.show({
          type: "error",
          text1: "Error",
          text2: "Failed to setup push notifications. Please try again.",
        });
        return;
      }

      // Register device with Novu
      const subscriberId = userId || 'unknown';
      const registered = await novuNotificationService.registerDevice(subscriberId, pushToken);
      
      if (!registered) {
        Toast.show({
          type: "error",
          text1: "Error",
          text2: "Failed to register device. Please try again.",
        });
        return;
      }

      // Update notification settings
      await updateNotificationSettings(token, pushToken);
    } catch (error) {
      console.error('Error enabling notifications:', error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "An unexpected error occurred. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setIsModalOpen(false);
  };

  return (
    <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <View className="flex flex-row items-center justify-between mb-4">
            <View />
            <Pressable onPress={handleSkip}>
              <IconX size={20} color="#a1a1aa" />
            </Pressable>
          </View>
          <Text className="text-xl text-center font-semibold text-zinc-200">
            You need to enable notifications
          </Text>
          <Text className="text-md text-center text-zinc-400">
            Allyson will need your help with tasks and the best way to get ahold
            of you is via notifications. We use Novu to deliver reliable notifications
            across multiple channels.
          </Text>
        </DialogHeader>
        
        <View className="mt-6 space-y-4">
          <Button
            variant="outline"
            className="w-full flex-row items-center justify-center gap-2"
            onPress={handleContinue}
            disabled={isLoading}
          >
            <Text className="text-sm text-zinc-400">
              {isLoading ? "Setting up..." : "Enable Notifications"}
            </Text>
          </Button>
          
          <Button
            variant="ghost"
            className="w-full flex-row items-center justify-center gap-2"
            onPress={handleSkip}
            disabled={isLoading}
          >
            <Text className="text-sm text-zinc-500">
              Skip for now
            </Text>
          </Button>
        </View>
      </DialogContent>
    </Dialog>
  );
}
