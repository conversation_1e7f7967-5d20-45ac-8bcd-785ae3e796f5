import React, { useState, useEffect } from "react";
import { View, Platform, Pressable } from "react-native";
import { useAuth } from "@clerk/clerk-expo";
import Toast from "react-native-toast-message";
import { Dialog, DialogContent, DialogHeader } from "~/components/ui/dialog";
import { Text } from "~/components/ui/typography";
import { Card } from "~/components/ui/card";
import { Switch } from "~/components/ui/switch";
import { IconX } from "@tabler/icons-react-native";
import * as Haptics from "expo-haptics";
import { useUser } from "~/context/UserContext";
import { useMCP } from "~/context/MCPContext";
import { novuNotificationService } from "~/lib/novuService";

export default function NovuNotificationsDialog({ isModalOpen, setIsModalOpen }) {
  const { getToken } = useAuth();
  const { user } = useUser();
  const { isConnected } = useMCP();
  const [notificationSettings, setNotificationSettings] = useState({
    mobile: false,
    email: false,
  });
  const [mcpNotifications, setMcpNotifications] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (user?.notificationSettings) {
      setNotificationSettings({
        mobile: user.notificationSettings.mobile || false,
        email: user.notificationSettings.email || false,
      });
    }
  }, [user]);

  /**
   * Register for push notifications using Novu
   */
  async function registerForPushNotificationsAsync() {
    try {
      setIsLoading(true);

      // Check permissions first
      const permissionStatus = await novuNotificationService.checkPermissionStatus();
      
      if (permissionStatus.status !== 'granted') {
        const requestResult = await novuNotificationService.requestPermissions();
        if (requestResult.status !== 'granted') {
          Toast.show({
            type: 'error',
            text1: 'Permission Required',
            text2: 'Please enable notifications in your device settings to receive updates from Allyson.',
          });
          return null;
        }
      }

      // Get push token
      const pushToken = await novuNotificationService.getPushToken();
      if (!pushToken) {
        throw new Error('Failed to get push token');
      }

      // Register device with Novu (this would typically be done through your backend)
      const subscriberId = user?.id || 'unknown';
      const registered = await novuNotificationService.registerDevice(subscriberId, pushToken);
      
      if (!registered) {
        throw new Error('Failed to register device with Novu');
      }

      return pushToken;
    } catch (error) {
      console.error('Error registering for push notifications:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Failed to setup push notifications. Please try again.',
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }

  /**
   * Handle notification setting toggle
   */
  const handleToggleChange = async (settingName: 'mobile' | 'email', newValue: boolean) => {
    try {
      setIsLoading(true);
      
      const updatedSettings = {
        ...notificationSettings,
        [settingName]: newValue,
      };
      
      setNotificationSettings(updatedSettings);

      // If mobile notifications are being enabled, register for push notifications
      let pushToken = null;
      if (settingName === 'mobile' && newValue === true) {
        pushToken = await registerForPushNotificationsAsync();
        if (!pushToken) {
          // Revert the setting if registration failed
          setNotificationSettings(notificationSettings);
          return;
        }
      }

      // Update settings on the backend
      const token = await getToken();
      const success = await novuNotificationService.updateNotificationSettings(token, {
        mobile: updatedSettings.mobile,
        email: updatedSettings.email,
        subscriberId: user?.id || 'unknown',
        pushToken: pushToken || undefined,
      });

      if (!success) {
        // Revert the setting if update failed
        setNotificationSettings(notificationSettings);
      }
    } catch (error) {
      console.error('Error updating notification settings:', error);
      // Revert the setting on error
      setNotificationSettings(notificationSettings);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <View className="flex flex-row items-center justify-between mb-4">
            <View />
            <Pressable
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setIsModalOpen(false);
              }}
            >
              <IconX size={20} color="#a1a1aa" />
            </Pressable>
          </View>
          <Text className="text-xl text-center font-semibold text-zinc-200">
            Notification Settings
          </Text>
          <Text className="text-md text-center text-zinc-400">
            Allyson will need your help with tasks and the best way to get ahold
            of you is via notifications. Configure your preferences below.
          </Text>
        </DialogHeader>
        
        <View className="rounded-xl p-1 mt-10">
          <Card className="w-full rounded-xl p-5 flex flex-row items-center justify-between">
            <View className="flex flex-col">
              <Text className="text-zinc-400 font-bold">
                Mobile Notifications
              </Text>
              <Text className="text-zinc-500 text-xs mt-1">
                Powered by Novu
              </Text>
            </View>
            <Switch
              onCheckedChange={(newValue) => handleToggleChange('mobile', newValue)}
              checked={notificationSettings.mobile}
              disabled={isLoading}
            />
          </Card>
          
          <Card className="mt-5 p-5 w-full flex flex-row items-center justify-between">
            <View className="flex flex-col">
              <Text className="text-zinc-400 font-bold">
                Email Notifications
              </Text>
              <Text className="text-zinc-500 text-xs mt-1">
                Updates and alerts via email
              </Text>
            </View>
            <Switch
              onCheckedChange={(newValue) => handleToggleChange('email', newValue)}
              checked={notificationSettings.email}
              disabled={isLoading}
            />
          </Card>
          
          <Card className="mt-5 p-5 w-full flex flex-row items-center justify-between">
            <View className="flex flex-col">
              <Text className="text-zinc-400 font-bold">
                MCP Dialog Alerts
              </Text>
              <Text className="text-zinc-500 text-xs mt-1">
                Human-in-the-loop dialog requests{isConnected ? ' (Connected)' : ' (Disconnected)'}
              </Text>
            </View>
            <Switch
              onCheckedChange={setMcpNotifications}
              checked={mcpNotifications}
              disabled={isLoading}
            />
          </Card>
        </View>
      </DialogContent>
    </Dialog>
  );
}
