import * as React from "react"
import Svg, { <PERSON>, Defs, LinearGradient, Stop } from "react-native-svg"
const Logo = ({ width = 251, height = 166, ...props }) => (
  <Svg
    width={width}
    height={height}
    fill="none"
    {...props}
  >
    <Path
      fill="url(#a)"
      fillRule="evenodd"
      d="M164.827 4.807c5.281 8.412 82.489 134.674 84.735 141.001 2.559 7.21-2.177 12.967-9.061 16.42-4.785 2.402-10.394 2.803-39.22 2.803-28.908 0-34.39-.395-39.007-2.803-4.249-2.216-8.953-8.819-22.545-31.642-14.313-24.036-18.135-29.32-22.937-31.719-4.021-1.929-7.224-2.263-11.297-.562-4.656 1.945-7.96 6.347-20.801 27.715-24.574 40.884-21.76 39.015-58.75 39.015-14.124 0-25.678-.427-25.678-.951 0-.523 11.162-19.548 24.807-42.277 15.465-25.763 27.048-43.295 30.76-46.554 15.99-14.046 39.205-17.76 56.844-9.095 5.004 2.46 9.914 5.397 13.828 9.858 2.599 2.962 13.811 20.512 24.913 38.998 11.771 19.6 18.871 33.267 23.071 34.367 4.667 1.223 9.803-1.528 10.881-5.829 1.08-4.954-6.521-16.512-40.223-72.572C122.473 33.263 102.727.66 102.36 0h59.628l2.839 4.807Z"
      clipRule="evenodd"
    />
    <Defs>
      <LinearGradient
        id="a"
        x1={125.266}
        x2={125.266}
        y1={0}
        y2={165.049}
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#4CD3FF" />
        <Stop offset={1} stopColor="#D573F6" />
      </LinearGradient>
    </Defs>
  </Svg>
)
export default Logo
