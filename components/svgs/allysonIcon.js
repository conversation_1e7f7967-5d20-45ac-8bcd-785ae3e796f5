import * as React from "react"
import Svg, { <PERSON>, De<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Stop } from "react-native-svg"
const AllysonIcon = (props) => (
  <Svg
    xmlns="http://www.w3.org/2000/svg"
    width={871}
    height={576}
    fill="none"
    {...props}
  >
    <Path
      fill="url(#a)"
      fillRule="evenodd"
      d="M573.333 16.775c18.397 29.357 287.389 469.996 295.214 492.078 8.916 25.163-7.584 45.254-31.568 57.304-16.669 8.383-36.211 9.781-136.641 9.781-100.715 0-119.817-1.376-135.899-9.781-14.805-7.733-31.195-30.777-78.55-110.426-49.866-83.883-63.181-102.324-79.911-110.695-14.008-6.733-25.169-7.901-39.358-1.963-16.223 6.788-27.735 22.149-72.473 96.721-85.614 142.68-75.809 136.16-204.682 136.16-49.208 0-89.465-1.493-89.465-3.321 0-1.823 38.89-68.22 86.429-147.54 53.878-89.91 94.236-151.095 107.165-162.469 55.715-49.018 136.591-61.979 198.048-31.739 17.433 8.583 34.539 18.834 48.176 34.401 9.056 10.339 48.119 71.586 86.797 136.098 41.01 68.405 65.745 116.099 80.38 119.939 16.261 4.266 34.151-5.335 37.907-20.343 3.766-17.29-22.718-57.624-140.134-253.269C425.772 116.086 356.976 2.304 355.698 0H563.44l9.893 16.775Z"
      clipRule="evenodd"
    />
    <Defs>
      <RadialGradient
        id="a"
        cx={0}
        cy={0}
        r={1}
        gradientTransform="rotate(108.071 27.717 420.286) scale(1622.69 5456.72)"
        gradientUnits="userSpaceOnUse"
      >
        <Stop stopColor="#fff" />
        <Stop offset={1} stopColor="#fff" stopOpacity={0.5} />
      </RadialGradient>
    </Defs>
  </Svg>
)
export default AllysonIcon
