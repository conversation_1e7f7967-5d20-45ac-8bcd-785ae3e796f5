import React from 'react';
import { ConvexProvider, ConvexReactClient } from 'convex/react';
import { useAuth } from '@clerk/clerk-expo';

const convex = new ConvexReactClient(process.env.EXPO_PUBLIC_CONVEX_URL!);

export function ConvexProviderWithClerk({ children }: { children: React.ReactNode }) {
  const { getToken, isLoaded } = useAuth();

  if (!isLoaded) {
    return null;
  }

  return (
    <ConvexProvider 
      client={convex}
      authTokenProvider={async () => {
        const token = await getToken({ template: "convex" });
        return token ?? null;
      }}
    >
      {children}
    </ConvexProvider>
  );
}