import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import Toast from 'react-native-toast-message';

export interface MCPDialog {
  id: string;
  type: 'input' | 'choice' | 'multiline' | 'confirmation' | 'info';
  title: string;
  message: string;
  options?: {
    defaultValue?: string;
    inputType?: 'text' | 'email' | 'password' | 'number';
    choices?: string[];
    allowMultiple?: boolean;
    confirmText?: string;
    cancelText?: string;
    buttonText?: string;
    variant?: 'default' | 'destructive' | 'success' | 'info';
  };
  timestamp: number;
}

export interface MCPResponse {
  id: string;
  success: boolean;
  data?: any;
  error?: string;
}

interface MCPContextType {
  isConnected: boolean;
  currentDialog: MCPDialog | null;
  connect: (url?: string) => void;
  disconnect: () => void;
  respondToDialog: (response: MCPResponse) => void;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  lastError: string | null;
}

const MCPContext = createContext<MCPContextType | undefined>(undefined);

export const useMCP = () => {
  const context = useContext(MCPContext);
  if (!context) {
    throw new Error('useMCP must be used within an MCPProvider');
  }
  return context;
};

interface MCPProviderProps {
  children: React.ReactNode;
  serverUrl?: string;
}

export const MCPProvider: React.FC<MCPProviderProps> = ({
  children,
  serverUrl = 'ws://localhost:3001'
}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [currentDialog, setCurrentDialog] = useState<MCPDialog | null>(null);
  const [lastError, setLastError] = useState<string | null>(null);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = (url: string = serverUrl) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return; // Already connected
    }

    setConnectionStatus('connecting');
    setLastError(null);

    try {
      const ws = new WebSocket(url);
      wsRef.current = ws;

      ws.onopen = () => {
        console.log('MCP WebSocket connected');
        setIsConnected(true);
        setConnectionStatus('connected');
        reconnectAttempts.current = 0;
        
        Toast.show({
          type: 'success',
          text1: 'MCP Connected',
          text2: 'Human-in-the-loop interface is now active',
        });
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          if (data.type === 'dialog') {
            const dialog: MCPDialog = {
              id: data.id,
              type: data.dialogType,
              title: data.title,
              message: data.message,
              options: data.options || {},
              timestamp: Date.now()
            };
            
            setCurrentDialog(dialog);
            
            // Show notification for new dialog
            Toast.show({
              type: 'info',
              text1: 'Action Required',
              text2: dialog.title,
              autoHide: false,
            });
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      ws.onclose = (event) => {
        console.log('MCP WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        setConnectionStatus('disconnected');
        
        // Attempt to reconnect if not manually disconnected
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          scheduleReconnect();
        }
      };

      ws.onerror = (error) => {
        console.error('MCP WebSocket error:', error);
        setConnectionStatus('error');
        setLastError('WebSocket connection failed');
        
        Toast.show({
          type: 'error',
          text1: 'MCP Connection Error',
          text2: 'Failed to connect to human-in-the-loop server',
        });
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setConnectionStatus('error');
      setLastError('Failed to create connection');
    }
  };

  const scheduleReconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
    reconnectAttempts.current++;

    console.log(`Scheduling MCP reconnect attempt ${reconnectAttempts.current} in ${delay}ms`);

    reconnectTimeoutRef.current = setTimeout(() => {
      connect();
    }, delay);
  };

  const disconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'Manual disconnect');
      wsRef.current = null;
    }

    setIsConnected(false);
    setConnectionStatus('disconnected');
    setCurrentDialog(null);
    setLastError(null);
    reconnectAttempts.current = 0;
  };

  const respondToDialog = (response: MCPResponse) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      console.error('Cannot send response: WebSocket not connected');
      return;
    }

    try {
      wsRef.current.send(JSON.stringify({
        type: 'response',
        ...response
      }));

      // Clear current dialog after response
      setCurrentDialog(null);

      // Hide any active toast notifications
      Toast.hide();

    } catch (error) {
      console.error('Failed to send dialog response:', error);
      Toast.show({
        type: 'error',
        text1: 'Response Failed',
        text2: 'Unable to send response to MCP server',
      });
    }
  };

  // Auto-connect on mount
  useEffect(() => {
    connect();

    return () => {
      disconnect();
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  const value: MCPContextType = {
    isConnected,
    currentDialog,
    connect,
    disconnect,
    respondToDialog,
    connectionStatus,
    lastError,
  };

  return (
    <MCPContext.Provider value={value}>
      {children}
    </MCPContext.Provider>
  );
};