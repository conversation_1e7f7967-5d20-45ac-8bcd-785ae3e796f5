import React, { useEffect, useState } from "react";
import {
  View,
  Image,
  KeyboardAvoidingView,
  Platform,
  Text,
  Alert,
  TouchableOpacity,
} from "react-native";
import * as Haptics from "expo-haptics";
import { H3, P } from "~/components/ui/typography";
import GradientButton from "~/components/GradientButton";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { router } from "expo-router";
import { IconBell } from "@tabler/icons-react-native";
import { useUser } from "~/context/UserContext";
import { useAuth } from "@clerk/clerk-expo";
import { novuNotificationService } from "~/lib/novuService";
import Toast from "react-native-toast-message";

export default function NotificationsScreen() {
  const [permissionStatus, setPermissionStatus] = useState(null);
  const { user } = useUser();
  const { isLoaded, userId, getToken } = useAuth();

  useEffect(() => {
    checkNotificationPermissions();
  }, []);

  const checkNotificationPermissions = async () => {
    const status = await novuNotificationService.checkPermissionStatus();
    setPermissionStatus(status.status);
  };

  const requestNotificationPermissions = async () => {
    const result = await novuNotificationService.requestPermissions();
    setPermissionStatus(result.status);
    return result.status;
  };

  const getPushToken = async () => {
    const token = await novuNotificationService.getPushToken();
    return token;
  };

  const updateNotificationSettings = async (token, pushToken) => {
    try {
      const success = await novuNotificationService.updateNotificationSettings(token, {
        mobile: true,
        email: user?.notificationSettings?.email || false,
        subscriberId: userId,
        pushToken: pushToken || undefined,
      });

      if (success) {
        Toast.show({
          type: "success",
          text1: "Notifications enabled",
          text2: "You're all set to receive notifications via Novu!",
        });
      }

      return success;
    } catch (error) {
      console.error("Error updating notification settings:", error);
      return false;
    }
  };

  const handleContinue = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    const token = await getToken();

    try {
      if (permissionStatus !== "granted") {
        const status = await requestNotificationPermissions();
        if (status !== "granted") {
          Alert.alert(
            "Permission Required",
            "To receive notifications, please enable them in your device settings.",
            [{ text: "OK" }]
          );
          return;
        }
      }

      // Get push token
      const pushToken = await getPushToken();
      if (!pushToken) {
        Toast.show({
          type: "error",
          text1: "Error",
          text2: "Failed to setup push notifications. Continuing anyway...",
        });
      } else {
        // Register device with Novu
        const subscriberId = userId || 'unknown';
        const registered = await novuNotificationService.registerDevice(subscriberId, pushToken);

        if (registered) {
          // Update notification settings
          await updateNotificationSettings(token, pushToken);
        }
      }

      // Proceed to the next screen regardless of notification setup success
      router.push("/(auth)/(onboarding)/review");
    } catch (error) {
      console.error('Error in handleContinue:', error);
      Toast.show({
        type: "error",
        text1: "Error",
        text2: "Something went wrong, but we'll continue the setup.",
      });
      router.push("/(auth)/(onboarding)/review");
    }
  };

  const handleSkip = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.push("/(auth)/(onboarding)/review");
  };
  if (user && user.expoPushToken && user.notificationSettings.mobile) {
    router.push("/(auth)/(onboarding)/review");
  }
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View className="flex-1">
        <Image
          source={require("../../../assets/bg.png")}
          className="absolute w-full h-full"
          resizeMode="cover"
        />
        <View className="flex-1 justify-between items-center px-8 pb-8">
          <View className="flex-1 justify-center items-center">
            <View className="items-center">
              <View className="w-24 h-24 rounded-full bg-white/20 items-center justify-center mb-6">
                <IconBell size={40} color="#fff" />
              </View>
              <H3 className="text-center font-bold mb-2 text-zinc-200">
                Enable Notifications
              </H3>

              <P className="text-center mb-8 text-zinc-400">
                Allyson works in the background so enable notifications to get
                the latest updates when she needs you.
              </P>
            </View>
          </View>

          <View className="w-full">
            <GradientButton
              text={
                permissionStatus === "granted"
                  ? "Continue"
                  : "Enable Notifications"
              }
              onPress={handleContinue}
              className="w-full mb-4"
            />
            <TouchableOpacity onPress={handleSkip} className="mt-2 mb-3">
              <Text className="text-center text-zinc-300/30">Skip</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </GestureHandlerRootView>
  );
}
