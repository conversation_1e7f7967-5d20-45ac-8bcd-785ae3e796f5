import React, { useState } from 'react';
import { View, ScrollView, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useMCP } from '~/context/MCPContext';
import { Text } from '~/components/ui/typography';
import { Button } from '~/components/ui/button';
import { DynamicTextInput } from '~/components/ui/input';
import { Card } from '~/components/ui/card';
import { MCPConnectionManager, MCPStatusIndicator } from '~/components/mcp/status-indicator';
import { 
  IconSettings, 
  IconRefresh, 
  IconInfo,
  IconShield,
  IconNetwork,
  IconBell
} from '@tabler/icons-react-native';
import * as Haptics from 'expo-haptics';
import Toast from 'react-native-toast-message';

export default function MCPSettingsPage() {
  const { 
    isConnected, 
    connectionStatus, 
    lastError, 
    currentDialog, 
    connect, 
    disconnect 
  } = useMCP();
  
  const [serverUrl, setServerUrl] = useState('ws://localhost:3001');
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleServerUrlChange = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    
    if (!serverUrl.trim()) {
      Toast.show({
        type: 'error',
        text1: 'Invalid URL',
        text2: 'Please enter a valid WebSocket URL',
      });
      return;
    }

    try {
      // Disconnect if currently connected
      if (isConnected) {
        disconnect();
        // Wait a moment before reconnecting
        setTimeout(() => {
          connect(serverUrl.trim());
        }, 1000);
      } else {
        connect(serverUrl.trim());
      }
      
      Toast.show({
        type: 'success',
        text1: 'URL Updated',
        text2: 'Attempting to connect to new server',
      });
    } catch (error) {
      Toast.show({
        type: 'error',
        text1: 'Connection Failed',
        text2: 'Unable to connect to the specified server',
      });
    }
  };

  const handleTestConnection = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    if (isConnected) {
      Toast.show({
        type: 'success',
        text1: 'Connection Active',
        text2: 'MCP server is responding normally',
      });
    } else {
      connect(serverUrl);
    }
  };

  const showConnectionInfo = () => {
    Alert.alert(
      'MCP Connection Info',
      `Human-in-the-Loop MCP (Model Context Protocol) allows AI agents to request user input during task execution. This creates a seamless collaboration between AI and human intelligence.

Features:
• Real-time dialog requests
• Cross-platform notifications  
• Secure WebSocket communication
• Queue management
• Offline support

The connection is established automatically when the app starts and will reconnect if interrupted.`,
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      <ScrollView className="flex-1 p-4" showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View className="flex-row items-center gap-3 mb-6">
          <IconSettings size={28} color="#e4e4e7" />
          <Text className="text-2xl font-bold">MCP Settings</Text>
        </View>

        {/* Status Overview */}
        <Card className="p-4 mb-6">
          <View className="flex-row items-center justify-between mb-3">
            <Text className="text-lg font-semibold">Connection Status</Text>
            <MCPStatusIndicator size="md" />
          </View>
          
          <View className="space-y-2">
            <View className="flex-row justify-between">
              <Text className="text-muted-foreground">Status</Text>
              <Text className={`font-medium ${
                isConnected ? 'text-green-400' : 'text-gray-400'
              }`}>
                {connectionStatus.charAt(0).toUpperCase() + connectionStatus.slice(1)}
              </Text>
            </View>
            
            {currentDialog && (
              <View className="flex-row justify-between">
                <Text className="text-muted-foreground">Pending Dialog</Text>
                <Text className="font-medium text-yellow-400">
                  {currentDialog.title}
                </Text>
              </View>
            )}
            
            {lastError && (
              <View className="p-3 rounded-lg bg-red-500/10 border border-red-500/20 mt-2">
                <Text className="text-sm text-red-400">{lastError}</Text>
              </View>
            )}
          </View>
        </Card>

        {/* Server Configuration */}
        <Card className="p-4 mb-6">
          <View className="flex-row items-center gap-2 mb-4">
            <IconNetwork size={20} color="#e4e4e7" />
            <Text className="text-lg font-semibold">Server Configuration</Text>
          </View>
          
          <View className="space-y-4">
            <View>
              <Text className="text-sm text-muted-foreground mb-2">
                WebSocket Server URL
              </Text>
              <DynamicTextInput
                value={serverUrl}
                onChangeText={setServerUrl}
                placeholder="ws://localhost:3001"
                keyboardType="url"
                autoCapitalize="none"
                autoCorrect={false}
              />
              <Text className="text-xs text-muted-foreground mt-1">
                The WebSocket URL where your MCP server is running
              </Text>
            </View>
            
            <View className="flex-row gap-2">
              <Button 
                onPress={handleServerUrlChange}
                className="flex-1"
                disabled={connectionStatus === 'connecting'}
              >
                <Text>
                  {connectionStatus === 'connecting' ? 'Connecting...' : 'Update & Connect'}
                </Text>
              </Button>
              
              <Button 
                variant="outline"
                onPress={handleTestConnection}
                disabled={connectionStatus === 'connecting'}
              >
                <IconRefresh size={16} color="#e4e4e7" />
              </Button>
            </View>
          </View>
        </Card>

        {/* Connection Management */}
        <MCPConnectionManager className="mb-6" />

        {/* Advanced Settings */}
        <Card className="p-4 mb-6">
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center gap-2">
              <IconShield size={20} color="#e4e4e7" />
              <Text className="text-lg font-semibold">Advanced Settings</Text>
            </View>
            
            <Button
              variant="ghost"
              size="sm"
              onPress={() => setShowAdvanced(!showAdvanced)}
            >
              <Text className="text-blue-400">
                {showAdvanced ? 'Hide' : 'Show'}
              </Text>
            </Button>
          </View>
          
          {showAdvanced && (
            <View className="mt-4 space-y-4">
              <View className="p-3 rounded-lg bg-blue-500/10 border border-blue-500/20">
                <Text className="text-sm text-blue-400 mb-1">Security Note</Text>
                <Text className="text-xs text-muted-foreground">
                  Only connect to trusted MCP servers. All communication is encrypted via WebSocket Secure (WSS) when using HTTPS origins.
                </Text>
              </View>
              
              <View className="space-y-2">
                <Text className="text-sm font-medium">Connection Settings</Text>
                <Text className="text-xs text-muted-foreground">
                  • Auto-reconnect: Enabled
                </Text>
                <Text className="text-xs text-muted-foreground">
                  • Max retry attempts: 5
                </Text>
                <Text className="text-xs text-muted-foreground">
                  • Timeout: 5 minutes per dialog
                </Text>
                <Text className="text-xs text-muted-foreground">
                  • Queue size: Unlimited
                </Text>
              </View>
            </View>
          )}
        </Card>

        {/* Notifications */}
        <Card className="p-4 mb-6">
          <View className="flex-row items-center gap-2 mb-4">
            <IconBell size={20} color="#e4e4e7" />
            <Text className="text-lg font-semibold">Notifications</Text>
          </View>
          
          <View className="space-y-3">
            <View className="flex-row justify-between items-center">
              <Text className="text-muted-foreground">Dialog Alerts</Text>
              <Text className="text-green-400 font-medium">Enabled</Text>
            </View>
            
            <View className="flex-row justify-between items-center">
              <Text className="text-muted-foreground">Connection Status</Text>
              <Text className="text-green-400 font-medium">Enabled</Text>
            </View>
            
            <Text className="text-xs text-muted-foreground">
              Notifications help you stay aware of dialog requests and connection changes
            </Text>
          </View>
        </Card>

        {/* Info Section */}
        <Card className="p-4 mb-8">
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center gap-2">
              <IconInfo size={20} color="#e4e4e7" />
              <Text className="text-lg font-semibold">About MCP</Text>
            </View>
            
            <Button
              variant="ghost"
              size="sm"
              onPress={showConnectionInfo}
            >
              <Text className="text-blue-400">Learn More</Text>
            </Button>
          </View>
          
          <Text className="text-sm text-muted-foreground mt-2">
            Model Context Protocol enables seamless human-AI collaboration through real-time dialog requests.
          </Text>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}