import React, { useState, useEffect } from "react";
import SessionComponent from "~/components/browser/session";
import NovuEnableNotificationsDialog from "~/components/dialogs/novu-enable-notifications";
import { MCPDialogManager } from "~/components/dialogs/mcp-dialogs";
import { useUser } from "~/context/UserContext";

export default function Home() {
  const { user, loading } = useUser();
  const [isModalOpen, setIsModalOpen] = useState(false);
  useEffect(() => {
    if (!loading && !user?.notificationSettings.mobile) {
      setIsModalOpen(true);
    }
  }, [loading, user]);

  return (
    <>
      <SessionComponent />
      <NovuEnableNotificationsDialog
        isModalOpen={isModalOpen}
        setIsModalOpen={setIsModalOpen}
      />
      <MCPDialogManager />
    </>
  );
}
